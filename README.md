# VietJack Educational Platform - Database Schema

## 📚 Overview

This is a comprehensive Microsoft SQL Server database schema designed for an educational platform similar to VietJack. The database supports a full-featured learning management system with courses, assessments, community features, gamification, and AI-powered capabilities.

## 🎯 Key Features

### Core Functionality
- ✅ **User Management**: Authentication, authorization, role-based access control
- ✅ **Course Management**: Courses, lessons, materials, categories, grade levels
- ✅ **Assessment System**: Quizzes, questions, multiple question types, automated grading
- ✅ **Progress Tracking**: Enrollment tracking, lesson completion, course progress
- ✅ **Community Features**: Forum discussions, posts, replies, tags
- ✅ **Gamification**: Points, badges, leaderboards, achievements
- ✅ **Notifications**: Multi-channel notifications with preferences
- ✅ **Content Moderation**: Reporting system, approval workflows
- ✅ **Reviews & Ratings**: Course reviews, helpfulness voting
- ✅ **Bookmarks**: Save courses, lessons, questions, forum posts
- ✅ **Messaging**: Private and group conversations
- ✅ **Analytics**: User activity tracking, course analytics, system metrics
- ✅ **AI Features**: Content summaries, learning paths, quiz generation, essay grading
- ✅ **Media Management**: Centralized file storage and tracking
- ✅ **Search**: Full-text search for courses, lessons, questions, forum posts

## 📊 Database Statistics

- **Total Tables**: 50+
- **Total Views**: 5
- **Total Stored Procedures**: 3
- **Database Engine**: Microsoft SQL Server 2016+
- **Database Name**: VietJackEdu

## 🗂️ Table Categories

### 1. User Management (7 tables)
- Users
- Roles
- UserRoles
- Permissions
- RolePermissions

### 2. Content Structure (6 tables)
- Categories
- GradeLevels
- Courses
- Lessons
- CourseMaterials

### 3. Enrollment & Progress (2 tables)
- Enrollments
- LessonProgress

### 4. Assessment System (5 tables)
- Quizzes
- Questions
- AnswerOptions
- QuizAttempts
- UserAnswers

### 5. Community & Forum (5 tables)
- ForumCategories
- ForumPosts
- ForumReplies
- ForumTags
- PostTags

### 6. Engagement (4 tables)
- Bookmarks
- CourseReviews
- ReviewHelpfulness

### 7. Gamification (4 tables)
- Badges
- UserBadges
- UserPoints
- Leaderboard

### 8. Notifications (2 tables)
- Notifications
- NotificationPreferences

### 9. Moderation (1 table)
- ContentReports

### 10. AI Features (4 tables)
- AIContentSummaries
- AILearningPaths
- AIQuizGeneration
- AIEssayGrading

### 11. Messaging (3 tables)
- Conversations
- ConversationParticipants
- Messages

### 12. Analytics (3 tables)
- UserActivityLog
- CourseAnalytics
- SystemAnalytics

### 13. System Configuration (3 tables)
- SystemSettings
- AuditLog
- EmailTemplates

### 14. Media Management (2 tables)
- MediaFiles
- SearchHistory

## 🚀 Quick Start

### Installation

1. **Prerequisites**
   - Microsoft SQL Server 2016 or later
   - SQL Server Management Studio (SSMS) or Azure Data Studio

2. **Create Database**
   ```sql
   -- Open test2.sql in SSMS and execute
   -- This will create the database, tables, views, procedures, and initial data
   ```

3. **Verify Installation**
   ```sql
   USE VietJackEdu;
   SELECT COUNT(*) AS TableCount 
   FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_TYPE = 'BASE TABLE';
   -- Should return 50+
   ```

4. **Create Admin User**
   ```sql
   INSERT INTO Users (username, email, password_hash, full_name, is_active, is_email_verified)
   VALUES ('admin', '<EMAIL>', 'YOUR_HASHED_PASSWORD', 'Administrator', 1, 1);
   
   INSERT INTO UserRoles (user_id, role_id)
   VALUES (SCOPE_IDENTITY(), 1); -- Assign Admin role
   ```

For detailed installation and usage instructions, see [QUICK_START_GUIDE.md](QUICK_START_GUIDE.md)

## 📖 Documentation

- **[DATABASE_DOCUMENTATION.md](DATABASE_DOCUMENTATION.md)** - Comprehensive database documentation
  - Detailed table descriptions
  - Relationships and foreign keys
  - Indexing strategy
  - Security features
  - Performance optimization
  - Scalability considerations

- **[QUICK_START_GUIDE.md](QUICK_START_GUIDE.md)** - Quick start guide
  - Installation steps
  - Sample data creation
  - Common operations
  - Useful queries
  - Maintenance tasks
  - Troubleshooting

## 🔑 Key Features Explained

### Role-Based Access Control (RBAC)
The system implements a flexible RBAC system with:
- **4 Default Roles**: Admin, Instructor, Student, Moderator
- **24+ Permissions**: Granular control over system operations
- **Flexible Assignment**: Users can have multiple roles

### Course Structure
```
Categories (e.g., Mathematics, Science)
  └── Courses (e.g., Introduction to Algebra)
      ├── Lessons (Video, Text, Quiz, Assignment)
      ├── Quizzes (Practice, Graded, Final)
      └── Materials (PDFs, Documents, Resources)
```

### Assessment Types
- **Multiple Choice**: Traditional multiple choice questions
- **True/False**: Binary choice questions
- **Short Answer**: Text-based answers
- **Essay**: Long-form responses with AI grading
- **Matching**: Match items from two lists

### Gamification System
- **Points**: Earned through various activities
- **Badges**: Achievement-based rewards (Common, Rare, Epic, Legendary)
- **Leaderboard**: Competitive rankings based on points and achievements
- **Progress Tracking**: Visual progress indicators

### AI-Powered Features
1. **Content Summarization**: Automatic summaries of lessons and courses
2. **Learning Path Recommendations**: Personalized course suggestions
3. **Quiz Generation**: AI-generated quizzes from content
4. **Essay Grading**: Automated essay scoring with feedback

### Forum System
- **Categories**: Organized discussion topics
- **Posts & Replies**: Threaded discussions
- **Tags**: Content categorization
- **Moderation**: Approval workflows and reporting
- **Solution Marking**: Mark replies as accepted answers

## 🔍 Sample Queries

### Get Active Courses
```sql
SELECT * FROM vw_ActiveCourses
ORDER BY enrollment_count DESC;
```

### Enroll User in Course
```sql
EXEC sp_EnrollUserInCourse @user_id = 1, @course_id = 1;
```

### View User Progress
```sql
SELECT * FROM vw_UserCourseProgress
WHERE user_id = 1;
```

### Search Courses (Full-Text)
```sql
SELECT * FROM Courses
WHERE CONTAINS((title, description), 'programming OR math')
AND is_published = 1;
```

### Get Leaderboard
```sql
SELECT TOP 10 u.username, l.total_points, l.rank_position
FROM Leaderboard l
INNER JOIN Users u ON l.user_id = u.user_id
ORDER BY l.rank_position;
```

## 🛠️ Stored Procedures

### sp_EnrollUserInCourse
Enrolls a user in a course with validation and point awarding.

### sp_CompleteLessonProgress
Marks a lesson as completed and updates course progress.

### sp_UpdateLeaderboard
Recalculates leaderboard rankings based on user activities.

## 📊 Database Views

### vw_ActiveCourses
Lists all published courses with instructor and category information.

### vw_UserCourseProgress
Comprehensive view of user progress across enrolled courses.

### vw_PopularCourses
Top 100 courses by enrollment and rating.

### vw_ForumActivity
Forum posts with activity metrics.

### vw_UserStatistics
Aggregated user statistics and achievements.

## 🔒 Security Features

- **Password Hashing**: Secure password storage
- **Email Verification**: Email confirmation workflow
- **Password Reset**: Secure password reset with tokens
- **Role-Based Access**: Granular permission system
- **Audit Logging**: Track all admin actions
- **Soft Deletes**: Preserve data integrity
- **Content Moderation**: Approval workflows and reporting

## 📈 Performance Optimization

- **Strategic Indexing**: Indexes on frequently queried columns
- **Composite Indexes**: Multi-column indexes for complex queries
- **Full-Text Search**: Fast content search capabilities
- **Denormalization**: Cached counts for quick access
- **Views**: Pre-computed complex queries
- **Stored Procedures**: Optimized common operations

## 🔄 Scalability

The database is designed with scalability in mind:
- **Partitioning Ready**: Large tables use BIGINT for IDs
- **Caching Strategy**: Denormalized counts and aggregates
- **Read Replicas**: Views support read-only queries
- **Horizontal Scaling**: User data can be sharded
- **Efficient Data Types**: Optimized storage

## 🧪 Testing

### Sample Data Creation
The database includes scripts to create:
- Default roles and permissions
- Sample categories and grade levels
- Forum categories
- Default badges
- System settings
- Email templates

### Test Scenarios
1. User registration and authentication
2. Course creation and publishing
3. Student enrollment and progress tracking
4. Quiz creation and taking
5. Forum posting and replying
6. Badge earning and leaderboard updates
7. Notification creation and delivery
8. Content reporting and moderation

## 📋 Maintenance

### Daily Tasks
- Update leaderboard
- Clean expired tokens
- Generate system analytics

### Weekly Tasks
- Rebuild fragmented indexes
- Update statistics
- Archive old activity logs

### Monthly Tasks
- Review slow queries
- Analyze storage usage
- Review audit logs

## 🚧 Future Enhancements

Potential additions for future versions:
1. **Payment System**: Transactions, subscriptions, invoices
2. **Live Classes**: Scheduling, attendance, recordings
3. **Assignments**: Submission, grading, plagiarism detection
4. **Certificates**: Templates, generation, verification
5. **Mobile App Support**: Push tokens, device management
6. **Social Features**: Following, activity feeds, sharing
7. **Advanced Analytics**: Learning analytics, predictive models
8. **Multi-language**: Content translations, localization
9. **Video Streaming**: Integrated video hosting
10. **API Rate Limiting**: Throttling and quota management

## 📝 File Structure

```
vietjacksql/
├── test2.sql                      # Main database schema file
├── README.md                      # This file
├── DATABASE_DOCUMENTATION.md      # Comprehensive documentation
└── QUICK_START_GUIDE.md          # Quick start guide
```

## 🤝 Contributing

This database schema is designed for educational purposes and course projects. Feel free to:
- Extend the schema for your specific needs
- Add new tables or relationships
- Optimize queries and indexes
- Implement additional features

## 📄 License

This database schema is provided as-is for educational purposes.

## 👥 Support

For questions or issues:
1. Review the documentation files
2. Check the quick start guide
3. Examine the SQL comments in test2.sql
4. Review SQL Server error logs

## 🎓 Use Cases

This database schema is suitable for:
- **Educational Platforms**: Online learning websites
- **Corporate Training**: Employee training systems
- **Course Projects**: Database design assignments
- **Learning Management Systems**: Full-featured LMS
- **Tutoring Platforms**: One-on-one or group tutoring
- **Certification Programs**: Professional certification tracking
- **School Management**: K-12 or university systems

## ⚡ Performance Tips

1. **Use Stored Procedures**: For common operations
2. **Enable Query Store**: Monitor query performance
3. **Regular Maintenance**: Update statistics and rebuild indexes
4. **Implement Caching**: Cache frequently accessed data
5. **Use Read Replicas**: Separate read and write operations
6. **Monitor Slow Queries**: Identify and optimize bottlenecks
7. **Partition Large Tables**: For better performance at scale

## 🔗 Related Technologies

This database works well with:
- **Backend**: ASP.NET Core, Node.js, Python (Django/Flask)
- **ORM**: Entity Framework, Sequelize, SQLAlchemy
- **API**: REST, GraphQL
- **Frontend**: React, Vue.js, Angular
- **Mobile**: React Native, Flutter, Xamarin
- **Caching**: Redis, Memcached
- **Search**: Elasticsearch, Azure Search
- **Analytics**: Power BI, Tableau

---

**Version**: 1.0  
**Last Updated**: 2025-09-30  
**Database Engine**: Microsoft SQL Server 2016+  
**Author**: VietJack Development Team

For detailed information, please refer to the documentation files included in this repository.
