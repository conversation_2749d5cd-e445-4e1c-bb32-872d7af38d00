# VietJack Educational Platform - Quick Start Guide

## Prerequisites
- Microsoft SQL Server 2016 or later
- SQL Server Management Studio (SSMS) or Azure Data Studio
- Appropriate permissions to create databases

## Installation Steps

### Step 1: Create the Database
1. Open SQL Server Management Studio
2. Connect to your SQL Server instance
3. Open the `test2.sql` file
4. Execute the entire script (F5 or Execute button)

The script will:
- Drop existing VietJackEdu database (if exists)
- Create new VietJackEdu database
- Create all 50+ tables
- Create 5 views
- Create 3 stored procedures
- Insert initial data (roles, permissions, categories, etc.)
- Create indexes and full-text catalogs

### Step 2: Verify Installation
Run the following query to verify all tables were created:

```sql
USE VietJackEdu;
GO

SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    TABLE_TYPE
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;
```

You should see 50+ tables.

### Step 3: Create Admin User
Create your first admin user:

```sql
USE VietJackEdu;
GO

-- Insert admin user
INSERT INTO Users (username, email, password_hash, full_name, is_active, is_email_verified)
VALUES ('admin', '<EMAIL>', 'HASH_YOUR_PASSWORD_HERE', 'System Administrator', 1, 1);

-- Get the user_id
DECLARE @admin_user_id INT = SCOPE_IDENTITY();

-- Assign Admin role
INSERT INTO UserRoles (user_id, role_id)
VALUES (@admin_user_id, 1); -- 1 = Admin role

PRINT 'Admin user created with ID: ' + CAST(@admin_user_id AS VARCHAR);
```

**Important**: Replace 'HASH_YOUR_PASSWORD_HERE' with a properly hashed password using your application's password hashing algorithm (e.g., bcrypt, PBKDF2).

### Step 4: Create Sample Data (Optional)

#### Create Sample Instructor
```sql
-- Create instructor user
INSERT INTO Users (username, email, password_hash, full_name, is_active, is_email_verified)
VALUES ('instructor1', '<EMAIL>', 'HASH_PASSWORD', 'John Doe', 1, 1);

DECLARE @instructor_id INT = SCOPE_IDENTITY();

-- Assign Instructor role
INSERT INTO UserRoles (user_id, role_id)
VALUES (@instructor_id, 2); -- 2 = Instructor role
```

#### Create Sample Course
```sql
-- Create a sample course
INSERT INTO Courses (
    course_code, title, slug, description, short_description,
    category_id, grade_id, instructor_id, difficulty_level,
    is_free, is_published, created_by
)
VALUES (
    'MATH101', 
    'Introduction to Algebra',
    'intro-algebra',
    'Learn the fundamentals of algebra including equations, functions, and graphing.',
    'Master algebra basics',
    1, -- Mathematics category
    9, -- Grade 9
    @instructor_id,
    'Beginner',
    1, -- Free course
    1, -- Published
    @instructor_id
);

DECLARE @course_id INT = SCOPE_IDENTITY();
PRINT 'Sample course created with ID: ' + CAST(@course_id AS VARCHAR);
```

#### Create Sample Lessons
```sql
-- Create sample lessons
INSERT INTO Lessons (course_id, title, slug, content, lesson_type, display_order, is_preview, created_by)
VALUES 
(@course_id, 'Introduction to Variables', 'intro-variables', 
 'In this lesson, we will learn about variables and how they are used in algebra...', 
 'Text', 1, 1, @instructor_id),
 
(@course_id, 'Solving Linear Equations', 'solving-linear-equations',
 'Learn how to solve linear equations step by step...', 
 'Video', 2, 0, @instructor_id),
 
(@course_id, 'Practice Quiz', 'practice-quiz',
 'Test your knowledge of variables and equations', 
 'Quiz', 3, 0, @instructor_id);
```

#### Create Sample Student
```sql
-- Create student user
INSERT INTO Users (username, email, password_hash, full_name, is_active, is_email_verified)
VALUES ('student1', '<EMAIL>', 'HASH_PASSWORD', 'Jane Smith', 1, 1);

DECLARE @student_id INT = SCOPE_IDENTITY();

-- Assign Student role
INSERT INTO UserRoles (user_id, role_id)
VALUES (@student_id, 3); -- 3 = Student role

-- Enroll student in course
EXEC sp_EnrollUserInCourse @user_id = @student_id, @course_id = @course_id;
```

## Common Operations

### Enrolling a User in a Course
```sql
EXEC sp_EnrollUserInCourse @user_id = 1, @course_id = 1;
```

### Marking a Lesson as Complete
```sql
DECLARE @enrollment_id INT;
SELECT @enrollment_id = enrollment_id 
FROM Enrollments 
WHERE user_id = 1 AND course_id = 1;

EXEC sp_CompleteLessonProgress 
    @user_id = 1, 
    @lesson_id = 1, 
    @enrollment_id = @enrollment_id;
```

### Updating Leaderboard
```sql
EXEC sp_UpdateLeaderboard;
```

### Viewing Active Courses
```sql
SELECT * FROM vw_ActiveCourses
ORDER BY enrollment_count DESC;
```

### Viewing User Progress
```sql
SELECT * FROM vw_UserCourseProgress
WHERE user_id = 1;
```

### Searching Courses (Full-Text Search)
```sql
SELECT course_id, title, description, rating_average
FROM Courses
WHERE CONTAINS((title, description), 'programming OR math')
  AND is_published = 1
ORDER BY rating_average DESC;
```

### Creating a Forum Post
```sql
INSERT INTO ForumPosts (forum_category_id, user_id, title, content, slug)
VALUES (
    2, -- Study Help category
    1, -- User ID
    'How do I solve quadratic equations?',
    'I am having trouble understanding the quadratic formula. Can someone explain?',
    'how-solve-quadratic-equations'
);
```

### Adding a Reply to Forum Post
```sql
INSERT INTO ForumReplies (post_id, user_id, content)
VALUES (
    1, -- Post ID
    2, -- User ID
    'The quadratic formula is: x = (-b ± √(b²-4ac)) / 2a. Let me explain each part...'
);

-- Update post reply count
UPDATE ForumPosts 
SET reply_count = reply_count + 1,
    last_reply_by = 2,
    last_reply_at = GETDATE(),
    last_activity = GETDATE()
WHERE post_id = 1;
```

### Creating a Quiz
```sql
-- Create quiz
INSERT INTO Quizzes (course_id, title, description, quiz_type, time_limit, passing_score, created_by)
VALUES (
    1, -- Course ID
    'Algebra Basics Quiz',
    'Test your understanding of basic algebra concepts',
    'Graded',
    30, -- 30 minutes
    70, -- 70% passing score
    1 -- Created by user 1
);

DECLARE @quiz_id INT = SCOPE_IDENTITY();

-- Add a multiple choice question
INSERT INTO Questions (quiz_id, question_text, question_type, difficulty_level, points, created_by)
VALUES (
    @quiz_id,
    'What is the value of x in the equation: 2x + 5 = 15?',
    'MultipleChoice',
    'Easy',
    1,
    1
);

DECLARE @question_id INT = SCOPE_IDENTITY();

-- Add answer options
INSERT INTO AnswerOptions (question_id, option_text, is_correct, display_order)
VALUES 
(@question_id, 'x = 5', 1, 1),
(@question_id, 'x = 10', 0, 2),
(@question_id, 'x = 7.5', 0, 3),
(@question_id, 'x = 20', 0, 4);
```

### Awarding a Badge
```sql
-- Award "First Steps" badge to user
INSERT INTO UserBadges (user_id, badge_id)
VALUES (1, 1); -- Badge 1 = "First Steps"

-- Award points
INSERT INTO UserPoints (user_id, points, point_type, source_type, source_id, description)
VALUES (1, 10, 'Earned', 'BadgeEarned', 1, 'Earned First Steps badge');

-- Update leaderboard
EXEC sp_UpdateLeaderboard;
```

### Creating a Notification
```sql
INSERT INTO Notifications (
    user_id, notification_type, title, message, 
    related_type, related_id, action_url, priority
)
VALUES (
    1, -- User ID
    'CourseUpdate',
    'New Lesson Available',
    'A new lesson has been added to Introduction to Algebra',
    'Course',
    1, -- Course ID
    '/courses/intro-algebra',
    'Normal'
);
```

### Reporting Content
```sql
INSERT INTO ContentReports (
    reporter_id, reported_type, reported_id, 
    reported_user_id, reason, description
)
VALUES (
    1, -- Reporter user ID
    'ForumPost',
    5, -- Post ID
    3, -- User who created the post
    'Spam',
    'This post contains spam links and irrelevant content'
);
```

### Bookmarking Content
```sql
-- Bookmark a course
INSERT INTO Bookmarks (user_id, bookmarkable_type, bookmarkable_id, notes)
VALUES (1, 'Course', 1, 'Want to review this later');

-- Bookmark a question
INSERT INTO Bookmarks (user_id, bookmarkable_type, bookmarkable_id, notes)
VALUES (1, 'Question', 5, 'Difficult question, need to practice more');
```

## Useful Queries

### Get User's Enrolled Courses with Progress
```sql
SELECT 
    c.title AS course_title,
    e.enrollment_date,
    e.progress_percentage,
    e.status,
    e.last_accessed
FROM Enrollments e
INNER JOIN Courses c ON e.course_id = c.course_id
WHERE e.user_id = 1
ORDER BY e.last_accessed DESC;
```

### Get Top 10 Users on Leaderboard
```sql
SELECT TOP 10
    u.username,
    u.full_name,
    l.total_points,
    l.rank_position,
    l.courses_completed,
    l.badges_earned
FROM Leaderboard l
INNER JOIN Users u ON l.user_id = u.user_id
ORDER BY l.rank_position;
```

### Get Course Statistics
```sql
SELECT 
    c.title,
    c.enrollment_count,
    c.rating_average,
    c.rating_count,
    c.view_count,
    COUNT(DISTINCT l.lesson_id) AS total_lessons,
    COUNT(DISTINCT q.quiz_id) AS total_quizzes
FROM Courses c
LEFT JOIN Lessons l ON c.course_id = l.course_id AND l.is_published = 1
LEFT JOIN Quizzes q ON c.course_id = q.course_id AND q.is_published = 1
WHERE c.course_id = 1
GROUP BY c.title, c.enrollment_count, c.rating_average, c.rating_count, c.view_count;
```

### Get Unread Notifications
```sql
SELECT 
    notification_type,
    title,
    message,
    action_url,
    created_at
FROM Notifications
WHERE user_id = 1 AND is_read = 0
ORDER BY priority DESC, created_at DESC;
```

### Get Recent Forum Activity
```sql
SELECT TOP 20
    fp.title,
    fp.slug,
    fc.category_name,
    u.username AS author,
    fp.reply_count,
    fp.view_count,
    fp.created_at,
    fp.last_activity
FROM ForumPosts fp
INNER JOIN ForumCategories fc ON fp.forum_category_id = fc.forum_category_id
INNER JOIN Users u ON fp.user_id = u.user_id
WHERE fp.deleted_at IS NULL AND fp.is_approved = 1
ORDER BY fp.last_activity DESC;
```

## Maintenance

### Daily Maintenance Script
```sql
-- Update leaderboard
EXEC sp_UpdateLeaderboard;

-- Clean up expired password reset tokens
UPDATE Users 
SET password_reset_token = NULL, 
    password_reset_expires = NULL
WHERE password_reset_expires < GETDATE();

-- Update system analytics
INSERT INTO SystemAnalytics (date, total_users, active_users, new_users, total_courses, total_enrollments)
SELECT 
    CAST(GETDATE() AS DATE),
    (SELECT COUNT(*) FROM Users WHERE is_active = 1 AND deleted_at IS NULL),
    (SELECT COUNT(DISTINCT user_id) FROM UserActivityLog WHERE created_at >= DATEADD(day, -1, GETDATE())),
    (SELECT COUNT(*) FROM Users WHERE CAST(created_at AS DATE) = CAST(GETDATE() AS DATE)),
    (SELECT COUNT(*) FROM Courses WHERE is_published = 1 AND deleted_at IS NULL),
    (SELECT COUNT(*) FROM Enrollments WHERE status = 'Active');
```

### Weekly Maintenance Script
```sql
-- Rebuild fragmented indexes
DECLARE @TableName NVARCHAR(255);
DECLARE @IndexName NVARCHAR(255);
DECLARE @SQL NVARCHAR(MAX);

DECLARE index_cursor CURSOR FOR
SELECT 
    OBJECT_NAME(ips.object_id) AS TableName,
    i.name AS IndexName
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 30
  AND ips.page_count > 1000;

OPEN index_cursor;
FETCH NEXT FROM index_cursor INTO @TableName, @IndexName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'ALTER INDEX ' + @IndexName + ' ON ' + @TableName + ' REBUILD;';
    EXEC sp_executesql @SQL;
    FETCH NEXT FROM index_cursor INTO @TableName, @IndexName;
END

CLOSE index_cursor;
DEALLOCATE index_cursor;

-- Update statistics
EXEC sp_updatestats;
```

## Troubleshooting

### Issue: Full-text search not working
**Solution**: Ensure SQL Server Full-Text Search feature is installed and the full-text catalog is created.

### Issue: Slow queries
**Solution**: 
1. Check index fragmentation
2. Update statistics
3. Review execution plans
4. Consider adding missing indexes

### Issue: Deadlocks
**Solution**:
1. Review transaction isolation levels
2. Keep transactions short
3. Access tables in consistent order
4. Consider using NOLOCK hints for read-only queries (with caution)

## Security Recommendations

1. **Password Hashing**: Always hash passwords using strong algorithms (bcrypt, PBKDF2, Argon2)
2. **SQL Injection**: Use parameterized queries in your application
3. **Least Privilege**: Grant minimum necessary permissions to application users
4. **Encryption**: Enable Transparent Data Encryption (TDE) for sensitive data
5. **Audit**: Regularly review audit logs for suspicious activity
6. **Backup**: Implement automated backup strategy with off-site storage

## Next Steps

1. Integrate with your application backend
2. Implement authentication and authorization logic
3. Create API endpoints for database operations
4. Set up automated backups
5. Configure monitoring and alerting
6. Implement caching strategy
7. Set up development, staging, and production environments
8. Create database migration scripts for version control

## Support

For issues or questions:
- Review the DATABASE_DOCUMENTATION.md file
- Check SQL Server error logs
- Review application logs
- Contact your database administrator

---

**Version**: 1.0  
**Last Updated**: 2025-09-30
