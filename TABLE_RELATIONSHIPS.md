# VietJack Educational Platform - Table Relationships

## Overview
This document describes the relationships between tables in the VietJack Educational Platform database.

## Core Entity Relationships

### User Management Flow
```
Users (1) ──→ (M) UserRoles (M) ──→ (1) Roles
                                            │
                                            └──→ (M) RolePermissions (M) ──→ (1) Permissions
```

**Description**: Users can have multiple roles, and each role has multiple permissions. This implements a flexible Role-Based Access Control (RBAC) system.

### Course Structure
```
Categories (1) ──→ (M) Courses (1) ──→ (M) Lessons
                         │                    │
                         │                    └──→ (M) CourseMaterials
                         │
                         └──→ (M) Quizzes (1) ──→ (M) Questions (1) ──→ (M) AnswerOptions
```

**Description**: Categories organize courses, which contain lessons and quizzes. Quizzes contain questions, and questions have answer options.

### Enrollment & Progress
```
Users (1) ──→ (M) Enrollments (M) ──→ (1) Courses
                    │
                    └──→ (M) LessonProgress (M) ──→ (1) Lessons
                    │
                    └──→ (M) QuizAttempts (M) ──→ (1) Quizzes
                              │
                              └──→ (M) UserAnswers (M) ──→ (1) Questions
```

**Description**: Users enroll in courses, track lesson progress, take quizzes, and submit answers.

### Forum System
```
ForumCategories (1) ──→ (M) ForumPosts (1) ──→ (M) ForumReplies
                                  │                      │
                                  │                      └──→ (M) ForumReplies (self-referencing)
                                  │
                                  └──→ (M) PostTags (M) ──→ (1) ForumTags
```

**Description**: Forum posts are organized by categories, can have multiple tags, and support nested replies.

### Gamification System
```
Users (1) ──→ (M) UserBadges (M) ──→ (1) Badges
       │
       └──→ (M) UserPoints
       │
       └──→ (1) Leaderboard
```

**Description**: Users earn badges and points, which are aggregated in the leaderboard.

## Detailed Table Relationships

### 1. Users Table
**Relationships:**
- **UserRoles** (1:M) - A user can have multiple roles
- **Enrollments** (1:M) - A user can enroll in multiple courses
- **Courses** (1:M as instructor) - An instructor can create multiple courses
- **ForumPosts** (1:M) - A user can create multiple forum posts
- **ForumReplies** (1:M) - A user can write multiple replies
- **QuizAttempts** (1:M) - A user can attempt multiple quizzes
- **Notifications** (1:M) - A user can receive multiple notifications
- **UserBadges** (1:M) - A user can earn multiple badges
- **UserPoints** (1:M) - A user has multiple point transactions
- **Leaderboard** (1:1) - Each user has one leaderboard entry
- **Bookmarks** (1:M) - A user can bookmark multiple items
- **CourseReviews** (1:M) - A user can review multiple courses
- **ContentReports** (1:M) - A user can report multiple items
- **ConversationParticipants** (1:M) - A user can participate in multiple conversations
- **Messages** (1:M) - A user can send multiple messages

### 2. Courses Table
**Relationships:**
- **Categories** (M:1) - Each course belongs to one category
- **GradeLevels** (M:1) - Each course is for one grade level
- **Users** (M:1 as instructor) - Each course has one instructor
- **Lessons** (1:M) - A course has multiple lessons
- **Enrollments** (1:M) - A course can have multiple enrollments
- **Quizzes** (1:M) - A course can have multiple quizzes
- **CourseMaterials** (1:M) - A course can have multiple materials
- **CourseReviews** (1:M) - A course can have multiple reviews
- **CourseAnalytics** (1:M) - A course has daily analytics records

### 3. Enrollments Table
**Relationships:**
- **Users** (M:1) - Each enrollment belongs to one user
- **Courses** (M:1) - Each enrollment is for one course
- **LessonProgress** (1:M) - An enrollment tracks progress for multiple lessons
- **QuizAttempts** (1:M) - An enrollment can have multiple quiz attempts

### 4. Lessons Table
**Relationships:**
- **Courses** (M:1) - Each lesson belongs to one course
- **LessonProgress** (1:M) - A lesson can be tracked by multiple users
- **CourseMaterials** (1:M) - A lesson can have multiple materials

### 5. Quizzes Table
**Relationships:**
- **Courses** (M:1) - Each quiz belongs to one course
- **Lessons** (M:1, optional) - A quiz can be associated with a lesson
- **Questions** (1:M) - A quiz has multiple questions
- **QuizAttempts** (1:M) - A quiz can be attempted multiple times

### 6. Questions Table
**Relationships:**
- **Quizzes** (M:1, optional) - A question can belong to a quiz
- **Categories** (M:1, optional) - A question can be categorized
- **GradeLevels** (M:1, optional) - A question can be for a grade level
- **AnswerOptions** (1:M) - A question has multiple answer options
- **UserAnswers** (1:M) - A question can be answered multiple times

### 7. QuizAttempts Table
**Relationships:**
- **Quizzes** (M:1) - Each attempt is for one quiz
- **Users** (M:1) - Each attempt is by one user
- **Enrollments** (M:1, optional) - An attempt can be linked to an enrollment
- **UserAnswers** (1:M) - An attempt has multiple answers

### 8. ForumPosts Table
**Relationships:**
- **ForumCategories** (M:1) - Each post belongs to one category
- **Users** (M:1) - Each post is created by one user
- **ForumReplies** (1:M) - A post can have multiple replies
- **PostTags** (1:M) - A post can have multiple tags

### 9. ForumReplies Table
**Relationships:**
- **ForumPosts** (M:1) - Each reply belongs to one post
- **Users** (M:1) - Each reply is written by one user
- **ForumReplies** (M:1, self-referencing) - A reply can be a response to another reply

### 10. Notifications Table
**Relationships:**
- **Users** (M:1) - Each notification is for one user

### 11. Bookmarks Table
**Relationships:**
- **Users** (M:1) - Each bookmark belongs to one user
- **Polymorphic** - Can reference Courses, Lessons, Questions, or ForumPosts

### 12. CourseReviews Table
**Relationships:**
- **Courses** (M:1) - Each review is for one course
- **Users** (M:1) - Each review is written by one user
- **Enrollments** (M:1, optional) - A review can be linked to an enrollment
- **ReviewHelpfulness** (1:M) - A review can have multiple helpfulness votes

### 13. Badges Table
**Relationships:**
- **UserBadges** (1:M) - A badge can be earned by multiple users

### 14. UserBadges Table
**Relationships:**
- **Users** (M:1) - Each badge award is for one user
- **Badges** (M:1) - Each award is for one badge

### 15. UserPoints Table
**Relationships:**
- **Users** (M:1) - Each point transaction is for one user

### 16. Leaderboard Table
**Relationships:**
- **Users** (1:1) - Each leaderboard entry is for one user

### 17. ContentReports Table
**Relationships:**
- **Users** (M:1 as reporter) - Each report is submitted by one user
- **Users** (M:1 as reported_user) - Each report is about one user's content
- **Users** (M:1 as reviewer) - Each report is reviewed by one user
- **Polymorphic** - Can reference ForumPosts, ForumReplies, CourseReviews, or Users

### 18. Conversations Table
**Relationships:**
- **Users** (M:1 as creator) - Each conversation is created by one user
- **ConversationParticipants** (1:M) - A conversation has multiple participants
- **Messages** (1:M) - A conversation has multiple messages

### 19. ConversationParticipants Table
**Relationships:**
- **Conversations** (M:1) - Each participant entry is for one conversation
- **Users** (M:1) - Each participant entry is for one user

### 20. Messages Table
**Relationships:**
- **Conversations** (M:1) - Each message belongs to one conversation
- **Users** (M:1 as sender) - Each message is sent by one user

## Polymorphic Relationships

### Bookmarks
Can reference multiple entity types:
- **Course** (bookmarkable_type = 'Course', bookmarkable_id = course_id)
- **Lesson** (bookmarkable_type = 'Lesson', bookmarkable_id = lesson_id)
- **Question** (bookmarkable_type = 'Question', bookmarkable_id = question_id)
- **ForumPost** (bookmarkable_type = 'ForumPost', bookmarkable_id = post_id)

### ContentReports
Can reference multiple entity types:
- **ForumPost** (reported_type = 'ForumPost', reported_id = post_id)
- **ForumReply** (reported_type = 'ForumReply', reported_id = reply_id)
- **CourseReview** (reported_type = 'CourseReview', reported_id = review_id)
- **User** (reported_type = 'User', reported_id = user_id)

### Notifications
Can reference multiple entity types via related_type and related_id:
- **Course** (related_type = 'Course', related_id = course_id)
- **ForumPost** (related_type = 'ForumPost', related_id = post_id)
- **Quiz** (related_type = 'Quiz', related_id = quiz_id)
- **Badge** (related_type = 'Badge', related_id = badge_id)

### MediaFiles
Can be associated with multiple entity types:
- **Course** (entity_type = 'Course', entity_id = course_id)
- **Lesson** (entity_type = 'Lesson', entity_id = lesson_id)
- **User** (entity_type = 'User', entity_id = user_id)
- **ForumPost** (entity_type = 'ForumPost', entity_id = post_id)

### UserActivityLog
Can track activity on multiple entity types:
- **Course** (entity_type = 'Course', entity_id = course_id)
- **Lesson** (entity_type = 'Lesson', entity_id = lesson_id)
- **Quiz** (entity_type = 'Quiz', entity_id = quiz_id)
- **ForumPost** (entity_type = 'ForumPost', entity_id = post_id)

### AuditLog
Can track changes to multiple entity types:
- **User** (entity_type = 'User', entity_id = user_id)
- **Course** (entity_type = 'Course', entity_id = course_id)
- **ForumPost** (entity_type = 'ForumPost', entity_id = post_id)

## Cascade Delete Behavior

### ON DELETE CASCADE
These relationships automatically delete child records when parent is deleted:
- **UserRoles** → Users
- **RolePermissions** → Roles, Permissions
- **Lessons** → Courses
- **CourseMaterials** → Courses, Lessons
- **Enrollments** → Users, Courses
- **LessonProgress** → Users, Enrollments
- **Quizzes** → Courses
- **Questions** → Quizzes
- **AnswerOptions** → Questions
- **QuizAttempts** → Quizzes, Users
- **UserAnswers** → QuizAttempts
- **ForumReplies** → ForumPosts
- **PostTags** → ForumPosts, ForumTags
- **Bookmarks** → Users
- **CourseReviews** → Courses, Users
- **UserBadges** → Users, Badges
- **UserPoints** → Users
- **Leaderboard** → Users
- **Notifications** → Users
- **ConversationParticipants** → Conversations, Users
- **Messages** → Conversations

### Soft Deletes (deleted_at column)
These tables support soft deletes:
- **Users**
- **Courses**
- **Lessons**
- **ForumPosts**
- **ForumReplies**

## Referential Integrity

All foreign key relationships enforce referential integrity:
1. **Cannot delete a parent** if child records exist (unless CASCADE is specified)
2. **Cannot insert a child** without a valid parent reference
3. **Cannot update a foreign key** to an invalid value

## Many-to-Many Relationships

### Users ↔ Roles
**Junction Table**: UserRoles
- user_id (FK to Users)
- role_id (FK to Roles)

### Roles ↔ Permissions
**Junction Table**: RolePermissions
- role_id (FK to Roles)
- permission_id (FK to Permissions)

### ForumPosts ↔ ForumTags
**Junction Table**: PostTags
- post_id (FK to ForumPosts)
- tag_id (FK to ForumTags)

### Users ↔ Conversations
**Junction Table**: ConversationParticipants
- conversation_id (FK to Conversations)
- user_id (FK to Users)

## Self-Referencing Relationships

### Categories
- **parent_category_id** references Categories(category_id)
- Allows hierarchical category structure

### ForumReplies
- **parent_reply_id** references ForumReplies(reply_id)
- Allows nested/threaded replies

## Unique Constraints

### Composite Unique Constraints
- **UserRoles**: (user_id, role_id) - A user can have each role only once
- **RolePermissions**: (role_id, permission_id) - A role can have each permission only once
- **Enrollments**: (user_id, course_id) - A user can enroll in each course only once
- **LessonProgress**: (user_id, lesson_id) - One progress record per user per lesson
- **CourseReviews**: (course_id, user_id) - One review per user per course
- **PostTags**: (post_id, tag_id) - Each tag can be applied to a post only once
- **Leaderboard**: (user_id) - One leaderboard entry per user

### Single Column Unique Constraints
- **Users**: username, email
- **Roles**: role_name
- **Permissions**: permission_name
- **Categories**: slug
- **Courses**: course_code, slug
- **GradeLevels**: grade_name
- **ForumCategories**: slug
- **ForumTags**: tag_name, slug
- **Badges**: badge_name
- **SystemSettings**: setting_key
- **EmailTemplates**: template_name

## Indexes for Performance

### Foreign Key Indexes
All foreign keys have indexes for efficient JOIN operations.

### Composite Indexes
- **Enrollments**: (user_id, course_id, status)
- **LessonProgress**: (user_id, lesson_id, is_completed)
- **QuizAttempts**: (user_id, quiz_id, submitted_at DESC)
- **ForumPosts**: (forum_category_id, created_at DESC)
- **Notifications**: (user_id, is_read, created_at DESC)
- **UserActivityLog**: (user_id, created_at DESC)

### Full-Text Indexes
- **Courses**: (title, description, short_description)
- **Lessons**: (title, content)
- **Questions**: (question_text, explanation)
- **ForumPosts**: (title, content)

## Data Flow Examples

### Course Enrollment Flow
1. User enrolls in Course → Creates **Enrollment** record
2. User views Lesson → Creates **UserActivityLog** entry
3. User completes Lesson → Creates/Updates **LessonProgress** record
4. System updates **Enrollment** progress_percentage
5. System awards points → Creates **UserPoints** record
6. System updates **Leaderboard**

### Quiz Taking Flow
1. User starts Quiz → Creates **QuizAttempt** record
2. User answers Questions → Creates **UserAnswers** records
3. User submits Quiz → Updates **QuizAttempt** with score
4. System awards points → Creates **UserPoints** record
5. System checks for badges → May create **UserBadges** record
6. System updates **Leaderboard**

### Forum Interaction Flow
1. User creates Post → Creates **ForumPost** record
2. User adds Tags → Creates **PostTags** records
3. Other users view Post → Updates **ForumPost** view_count
4. Other users Reply → Creates **ForumReply** records
5. Post author marks solution → Updates **ForumReply** is_solution
6. System awards points → Creates **UserPoints** records

---

**Version**: 1.0  
**Last Updated**: 2025-09-30
