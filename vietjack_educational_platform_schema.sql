-- =====================================================
-- VietJack Educational Platform Database Schema
-- Microsoft SQL Server Implementation
-- Comprehensive, Scalable, and Extensible Design
-- =====================================================

-- Enable ANSI_NULLS and QUOTED_IDENTIFIER for best practices
SET ANSI_NULLS ON;
SET QUOTED_IDENTIFIER ON;

-- =====================================================
-- CORE USER MANAGEMENT TABLES
-- =====================================================

-- Roles table for role-based access control
CREATE TABLE [dbo].[Roles] (
    [RoleId] INT IDENTITY(1,1) PRIMARY KEY,
    [RoleName] NVARCHAR(50) NOT NULL UNIQUE,
    [Description] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL
);

-- Permissions table for granular access control
CREATE TABLE [dbo].[Permissions] (
    [PermissionId] INT IDENTITY(1,1) PRIMARY KEY,
    [PermissionName] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(255),
    [Module] NVARCHAR(50) NOT NULL, -- e.g., 'Users', 'Courses', 'Forums'
    [Action] NVARCHAR(50) NOT NULL, -- e.g., 'Create', 'Read', 'Update', 'Delete'
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Role-Permission mapping table
CREATE TABLE [dbo].[RolePermissions] (
    [RolePermissionId] INT IDENTITY(1,1) PRIMARY KEY,
    [RoleId] INT NOT NULL,
    [PermissionId] INT NOT NULL,
    [IsGranted] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    CONSTRAINT FK_RolePermissions_Role FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Roles]([RoleId]),
    CONSTRAINT FK_RolePermissions_Permission FOREIGN KEY ([PermissionId]) REFERENCES [dbo].[Permissions]([PermissionId]),
    CONSTRAINT UQ_RolePermissions_RolePermission UNIQUE ([RoleId], [PermissionId])
);

-- Account tiers for premium/free functionality
CREATE TABLE [dbo].[AccountTiers] (
    [TierId] INT IDENTITY(1,1) PRIMARY KEY,
    [TierName] NVARCHAR(50) NOT NULL UNIQUE,
    [Description] NVARCHAR(255),
    [MonthlyPrice] DECIMAL(10,2) NOT NULL DEFAULT 0,
    [MaxCourses] INT NULL, -- NULL means unlimited
    [MaxStorageGB] INT NULL, -- NULL means unlimited
    [HasAIFeatures] BIT NOT NULL DEFAULT 0,
    [HasPrioritySupport] BIT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Main Users table
CREATE TABLE [dbo].[Users] (
    [UserId] INT IDENTITY(1,1) PRIMARY KEY,
    [Username] NVARCHAR(50) NOT NULL UNIQUE,
    [Email] NVARCHAR(255) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [Salt] NVARCHAR(255) NOT NULL,
    [FirstName] NVARCHAR(100) NOT NULL,
    [LastName] NVARCHAR(100) NOT NULL,
    [DateOfBirth] DATE NULL,
    [Gender] NVARCHAR(10) NULL CHECK ([Gender] IN ('Male', 'Female', 'Other', 'Prefer not to say')),
    [PhoneNumber] NVARCHAR(20) NULL,
    [ProfileImageUrl] NVARCHAR(500) NULL,
    [Bio] NVARCHAR(1000) NULL,
    [IsEmailVerified] BIT NOT NULL DEFAULT 0,
    [IsPhoneVerified] BIT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [IsSuspended] BIT NOT NULL DEFAULT 0,
    [SuspensionReason] NVARCHAR(500) NULL,
    [SuspendedUntil] DATETIME2(7) NULL,
    [LastLoginAt] DATETIME2(7) NULL,
    [LoginAttempts] INT NOT NULL DEFAULT 0,
    [LockedUntil] DATETIME2(7) NULL,
    [TierId] INT NOT NULL DEFAULT 1,
    [TierExpiresAt] DATETIME2(7) NULL,
    [PreferredLanguage] NVARCHAR(10) NOT NULL DEFAULT 'en',
    [TimeZone] NVARCHAR(50) NOT NULL DEFAULT 'UTC',
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Users_AccountTier FOREIGN KEY ([TierId]) REFERENCES [dbo].[AccountTiers]([TierId])
);

-- User-Role mapping table (many-to-many)
CREATE TABLE [dbo].[UserRoles] (
    [UserRoleId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [RoleId] INT NOT NULL,
    [AssignedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [AssignedBy] INT NULL,
    [ExpiresAt] DATETIME2(7) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_UserRoles_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_UserRoles_Role FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Roles]([RoleId]),
    CONSTRAINT FK_UserRoles_AssignedBy FOREIGN KEY ([AssignedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_UserRoles_UserRole UNIQUE ([UserId], [RoleId])
);

-- User authentication tokens (for password reset, email verification, etc.)
CREATE TABLE [dbo].[UserTokens] (
    [TokenId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [TokenType] NVARCHAR(50) NOT NULL, -- 'PasswordReset', 'EmailVerification', 'PhoneVerification'
    [Token] NVARCHAR(255) NOT NULL UNIQUE,
    [ExpiresAt] DATETIME2(7) NOT NULL,
    [IsUsed] BIT NOT NULL DEFAULT 0,
    [UsedAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserTokens_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- User sessions for tracking active sessions
CREATE TABLE [dbo].[UserSessions] (
    [SessionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [SessionToken] NVARCHAR(255) NOT NULL UNIQUE,
    [IpAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    [DeviceType] NVARCHAR(50) NULL, -- 'Desktop', 'Mobile', 'Tablet'
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [LastAccessedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [ExpiresAt] DATETIME2(7) NOT NULL,
    CONSTRAINT FK_UserSessions_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- =====================================================
-- EDUCATIONAL CONTENT STRUCTURE TABLES
-- =====================================================

-- Subject categories (Math, Science, Literature, etc.)
CREATE TABLE [dbo].[Subjects] (
    [SubjectId] INT IDENTITY(1,1) PRIMARY KEY,
    [SubjectName] NVARCHAR(100) NOT NULL,
    [SubjectCode] NVARCHAR(20) NOT NULL UNIQUE,
    [Description] NVARCHAR(500) NULL,
    [IconUrl] NVARCHAR(500) NULL,
    [ColorCode] NVARCHAR(7) NULL, -- Hex color code
    [ParentSubjectId] INT NULL, -- For subject hierarchy
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Subjects_Parent FOREIGN KEY ([ParentSubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_Subjects_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Subjects_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Grade levels
CREATE TABLE [dbo].[GradeLevels] (
    [GradeLevelId] INT IDENTITY(1,1) PRIMARY KEY,
    [GradeName] NVARCHAR(50) NOT NULL UNIQUE,
    [GradeNumber] INT NOT NULL UNIQUE,
    [Description] NVARCHAR(255) NULL,
    [AgeRangeMin] INT NULL,
    [AgeRangeMax] INT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Difficulty levels
CREATE TABLE [dbo].[DifficultyLevels] (
    [DifficultyId] INT IDENTITY(1,1) PRIMARY KEY,
    [DifficultyName] NVARCHAR(50) NOT NULL UNIQUE,
    [DifficultyLevel] INT NOT NULL UNIQUE, -- 1=Beginner, 2=Intermediate, 3=Advanced, etc.
    [Description] NVARCHAR(255) NULL,
    [ColorCode] NVARCHAR(7) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Courses table
CREATE TABLE [dbo].[Courses] (
    [CourseId] INT IDENTITY(1,1) PRIMARY KEY,
    [CourseTitle] NVARCHAR(255) NOT NULL,
    [CourseCode] NVARCHAR(50) NOT NULL UNIQUE,
    [Description] NVARCHAR(MAX) NULL,
    [ShortDescription] NVARCHAR(500) NULL,
    [SubjectId] INT NOT NULL,
    [GradeLevelId] INT NOT NULL,
    [DifficultyId] INT NOT NULL,
    [InstructorId] INT NOT NULL,
    [ThumbnailUrl] NVARCHAR(500) NULL,
    [PreviewVideoUrl] NVARCHAR(500) NULL,
    [EstimatedDurationHours] INT NULL,
    [Price] DECIMAL(10,2) NOT NULL DEFAULT 0,
    [IsFree] BIT NOT NULL DEFAULT 1,
    [IsPublished] BIT NOT NULL DEFAULT 0,
    [PublishedAt] DATETIME2(7) NULL,
    [IsFeatured] BIT NOT NULL DEFAULT 0,
    [ViewCount] INT NOT NULL DEFAULT 0,
    [EnrollmentCount] INT NOT NULL DEFAULT 0,
    [AverageRating] DECIMAL(3,2) NOT NULL DEFAULT 0,
    [RatingCount] INT NOT NULL DEFAULT 0,
    [Prerequisites] NVARCHAR(MAX) NULL,
    [LearningObjectives] NVARCHAR(MAX) NULL,
    [TargetAudience] NVARCHAR(500) NULL,
    [Language] NVARCHAR(10) NOT NULL DEFAULT 'en',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Courses_Subject FOREIGN KEY ([SubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_Courses_GradeLevel FOREIGN KEY ([GradeLevelId]) REFERENCES [dbo].[GradeLevels]([GradeLevelId]),
    CONSTRAINT FK_Courses_Difficulty FOREIGN KEY ([DifficultyId]) REFERENCES [dbo].[DifficultyLevels]([DifficultyId]),
    CONSTRAINT FK_Courses_Instructor FOREIGN KEY ([InstructorId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Courses_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Courses_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Course sections/modules
CREATE TABLE [dbo].[CourseSections] (
    [SectionId] INT IDENTITY(1,1) PRIMARY KEY,
    [CourseId] INT NOT NULL,
    [SectionTitle] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(1000) NULL,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_CourseSections_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId]),
    CONSTRAINT FK_CourseSections_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_CourseSections_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Lessons within course sections
CREATE TABLE [dbo].[Lessons] (
    [LessonId] INT IDENTITY(1,1) PRIMARY KEY,
    [SectionId] INT NOT NULL,
    [LessonTitle] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [Content] NVARCHAR(MAX) NULL, -- Rich text content
    [LessonType] NVARCHAR(50) NOT NULL DEFAULT 'Theory', -- 'Theory', 'Video', 'Interactive', 'Assignment'
    [VideoUrl] NVARCHAR(500) NULL,
    [VideoDurationSeconds] INT NULL,
    [EstimatedReadingMinutes] INT NULL,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsPreview] BIT NOT NULL DEFAULT 0, -- Can be viewed without enrollment
    [IsMandatory] BIT NOT NULL DEFAULT 1,
    [IsPublished] BIT NOT NULL DEFAULT 0,
    [PublishedAt] DATETIME2(7) NULL,
    [ViewCount] INT NOT NULL DEFAULT 0,
    [AverageRating] DECIMAL(3,2) NOT NULL DEFAULT 0,
    [RatingCount] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Lessons_Section FOREIGN KEY ([SectionId]) REFERENCES [dbo].[CourseSections]([SectionId]),
    CONSTRAINT FK_Lessons_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Lessons_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Lesson materials (files, documents, etc.)
CREATE TABLE [dbo].[LessonMaterials] (
    [MaterialId] INT IDENTITY(1,1) PRIMARY KEY,
    [LessonId] INT NOT NULL,
    [MaterialTitle] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [MaterialType] NVARCHAR(50) NOT NULL, -- 'PDF', 'Video', 'Audio', 'Image', 'Document', 'Link'
    [FileUrl] NVARCHAR(500) NULL,
    [FileName] NVARCHAR(255) NULL,
    [FileSize] BIGINT NULL, -- Size in bytes
    [MimeType] NVARCHAR(100) NULL,
    [ExternalUrl] NVARCHAR(500) NULL, -- For external links
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsDownloadable] BIT NOT NULL DEFAULT 1,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_LessonMaterials_Lesson FOREIGN KEY ([LessonId]) REFERENCES [dbo].[Lessons]([LessonId]),
    CONSTRAINT FK_LessonMaterials_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_LessonMaterials_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Course enrollments
CREATE TABLE [dbo].[CourseEnrollments] (
    [EnrollmentId] INT IDENTITY(1,1) PRIMARY KEY,
    [CourseId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [EnrollmentDate] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CompletionDate] DATETIME2(7) NULL,
    [ProgressPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [LastAccessedAt] DATETIME2(7) NULL,
    [IsCompleted] BIT NOT NULL DEFAULT 0,
    [IsCertified] BIT NOT NULL DEFAULT 0,
    [CertificateUrl] NVARCHAR(500) NULL,
    [FinalGrade] DECIMAL(5,2) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_CourseEnrollments_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId]),
    CONSTRAINT FK_CourseEnrollments_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_CourseEnrollments_CourseUser UNIQUE ([CourseId], [UserId])
);

-- Lesson progress tracking
CREATE TABLE [dbo].[LessonProgress] (
    [ProgressId] INT IDENTITY(1,1) PRIMARY KEY,
    [EnrollmentId] INT NOT NULL,
    [LessonId] INT NOT NULL,
    [StartedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CompletedAt] DATETIME2(7) NULL,
    [TimeSpentSeconds] INT NOT NULL DEFAULT 0,
    [ProgressPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [IsCompleted] BIT NOT NULL DEFAULT 0,
    [LastAccessedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_LessonProgress_Enrollment FOREIGN KEY ([EnrollmentId]) REFERENCES [dbo].[CourseEnrollments]([EnrollmentId]),
    CONSTRAINT FK_LessonProgress_Lesson FOREIGN KEY ([LessonId]) REFERENCES [dbo].[Lessons]([LessonId]),
    CONSTRAINT UQ_LessonProgress_EnrollmentLesson UNIQUE ([EnrollmentId], [LessonId])
);

-- =====================================================
-- COMMUNITY AND FORUM FEATURES
-- =====================================================

-- Forum categories
CREATE TABLE [dbo].[ForumCategories] (
    [CategoryId] INT IDENTITY(1,1) PRIMARY KEY,
    [CategoryName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [IconUrl] NVARCHAR(500) NULL,
    [ColorCode] NVARCHAR(7) NULL,
    [ParentCategoryId] INT NULL,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [RequiresModeration] BIT NOT NULL DEFAULT 0,
    [MinRoleToPost] INT NULL, -- Minimum role required to post
    [MinRoleToView] INT NULL, -- Minimum role required to view
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_ForumCategories_Parent FOREIGN KEY ([ParentCategoryId]) REFERENCES [dbo].[ForumCategories]([CategoryId]),
    CONSTRAINT FK_ForumCategories_MinRoleToPost FOREIGN KEY ([MinRoleToPost]) REFERENCES [dbo].[Roles]([RoleId]),
    CONSTRAINT FK_ForumCategories_MinRoleToView FOREIGN KEY ([MinRoleToView]) REFERENCES [dbo].[Roles]([RoleId]),
    CONSTRAINT FK_ForumCategories_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumCategories_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Forum posts
CREATE TABLE [dbo].[ForumPosts] (
    [PostId] INT IDENTITY(1,1) PRIMARY KEY,
    [CategoryId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [Title] NVARCHAR(255) NOT NULL,
    [Content] NVARCHAR(MAX) NOT NULL,
    [PostType] NVARCHAR(50) NOT NULL DEFAULT 'Discussion', -- 'Discussion', 'Question', 'Announcement', 'Poll'
    [IsSticky] BIT NOT NULL DEFAULT 0,
    [IsLocked] BIT NOT NULL DEFAULT 0,
    [IsFeatured] BIT NOT NULL DEFAULT 0,
    [ViewCount] INT NOT NULL DEFAULT 0,
    [ReplyCount] INT NOT NULL DEFAULT 0,
    [LastReplyAt] DATETIME2(7) NULL,
    [LastReplyUserId] INT NULL,
    [IsApproved] BIT NOT NULL DEFAULT 1,
    [ApprovedBy] INT NULL,
    [ApprovedAt] DATETIME2(7) NULL,
    [IsReported] BIT NOT NULL DEFAULT 0,
    [ReportCount] INT NOT NULL DEFAULT 0,
    [IsDeleted] BIT NOT NULL DEFAULT 0,
    [DeletedAt] DATETIME2(7) NULL,
    [DeletedBy] INT NULL,
    [DeleteReason] NVARCHAR(500) NULL,
    [Tags] NVARCHAR(500) NULL, -- Comma-separated tags
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [EditedAt] DATETIME2(7) NULL,
    [EditedBy] INT NULL,
    CONSTRAINT FK_ForumPosts_Category FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[ForumCategories]([CategoryId]),
    CONSTRAINT FK_ForumPosts_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumPosts_LastReplyUser FOREIGN KEY ([LastReplyUserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumPosts_ApprovedBy FOREIGN KEY ([ApprovedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumPosts_DeletedBy FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumPosts_EditedBy FOREIGN KEY ([EditedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Forum post replies/comments
CREATE TABLE [dbo].[ForumReplies] (
    [ReplyId] INT IDENTITY(1,1) PRIMARY KEY,
    [PostId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [ParentReplyId] INT NULL, -- For nested replies
    [Content] NVARCHAR(MAX) NOT NULL,
    [IsApproved] BIT NOT NULL DEFAULT 1,
    [ApprovedBy] INT NULL,
    [ApprovedAt] DATETIME2(7) NULL,
    [IsReported] BIT NOT NULL DEFAULT 0,
    [ReportCount] INT NOT NULL DEFAULT 0,
    [IsDeleted] BIT NOT NULL DEFAULT 0,
    [DeletedAt] DATETIME2(7) NULL,
    [DeletedBy] INT NULL,
    [DeleteReason] NVARCHAR(500) NULL,
    [LikeCount] INT NOT NULL DEFAULT 0,
    [DislikeCount] INT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [EditedAt] DATETIME2(7) NULL,
    [EditedBy] INT NULL,
    CONSTRAINT FK_ForumReplies_Post FOREIGN KEY ([PostId]) REFERENCES [dbo].[ForumPosts]([PostId]),
    CONSTRAINT FK_ForumReplies_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumReplies_ParentReply FOREIGN KEY ([ParentReplyId]) REFERENCES [dbo].[ForumReplies]([ReplyId]),
    CONSTRAINT FK_ForumReplies_ApprovedBy FOREIGN KEY ([ApprovedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumReplies_DeletedBy FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ForumReplies_EditedBy FOREIGN KEY ([EditedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Post and reply likes/dislikes
CREATE TABLE [dbo].[PostReactions] (
    [ReactionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PostId] INT NULL,
    [ReplyId] INT NULL,
    [ReactionType] NVARCHAR(20) NOT NULL, -- 'Like', 'Dislike', 'Love', 'Helpful', etc.
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_PostReactions_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_PostReactions_Post FOREIGN KEY ([PostId]) REFERENCES [dbo].[ForumPosts]([PostId]),
    CONSTRAINT FK_PostReactions_Reply FOREIGN KEY ([ReplyId]) REFERENCES [dbo].[ForumReplies]([ReplyId]),
    CONSTRAINT CK_PostReactions_OneTarget CHECK (([PostId] IS NOT NULL AND [ReplyId] IS NULL) OR ([PostId] IS NULL AND [ReplyId] IS NOT NULL)),
    CONSTRAINT UQ_PostReactions_UserPost UNIQUE ([UserId], [PostId]),
    CONSTRAINT UQ_PostReactions_UserReply UNIQUE ([UserId], [ReplyId])
);

-- Content bookmarks
CREATE TABLE [dbo].[Bookmarks] (
    [BookmarkId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [ContentType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'ForumPost', 'ForumReply'
    [ContentId] INT NOT NULL,
    [BookmarkName] NVARCHAR(255) NULL, -- Custom name for the bookmark
    [Notes] NVARCHAR(1000) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_Bookmarks_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_Bookmarks_UserContentType UNIQUE ([UserId], [ContentType], [ContentId])
);

-- Content reporting system
CREATE TABLE [dbo].[ContentReports] (
    [ReportId] INT IDENTITY(1,1) PRIMARY KEY,
    [ReportedBy] INT NOT NULL,
    [ContentType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'ForumPost', 'ForumReply', 'User'
    [ContentId] INT NOT NULL,
    [ReportReason] NVARCHAR(100) NOT NULL, -- 'Spam', 'Inappropriate', 'Copyright', 'Harassment', etc.
    [Description] NVARCHAR(1000) NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- 'Pending', 'Reviewed', 'Resolved', 'Dismissed'
    [ReviewedBy] INT NULL,
    [ReviewedAt] DATETIME2(7) NULL,
    [ReviewNotes] NVARCHAR(1000) NULL,
    [ActionTaken] NVARCHAR(500) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_ContentReports_ReportedBy FOREIGN KEY ([ReportedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ContentReports_ReviewedBy FOREIGN KEY ([ReviewedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Course and content ratings
CREATE TABLE [dbo].[ContentRatings] (
    [RatingId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [ContentType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson'
    [ContentId] INT NOT NULL,
    [Rating] INT NOT NULL CHECK ([Rating] >= 1 AND [Rating] <= 5),
    [Review] NVARCHAR(1000) NULL,
    [IsPublic] BIT NOT NULL DEFAULT 1,
    [IsVerifiedPurchase] BIT NOT NULL DEFAULT 0, -- For paid courses
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_ContentRatings_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_ContentRatings_UserContent UNIQUE ([UserId], [ContentType], [ContentId])
);

-- =====================================================
-- ASSESSMENT AND GAMIFICATION SYSTEM
-- =====================================================

-- Quiz/Assessment templates
CREATE TABLE [dbo].[Quizzes] (
    [QuizId] INT IDENTITY(1,1) PRIMARY KEY,
    [Title] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(1000) NULL,
    [Instructions] NVARCHAR(MAX) NULL,
    [QuizType] NVARCHAR(50) NOT NULL DEFAULT 'Practice', -- 'Practice', 'Assessment', 'Final', 'Certification'
    [CourseId] INT NULL, -- NULL for standalone quizzes
    [LessonId] INT NULL, -- NULL for course-level quizzes
    [SubjectId] INT NOT NULL,
    [DifficultyId] INT NOT NULL,
    [TimeLimit] INT NULL, -- Time limit in minutes
    [MaxAttempts] INT NULL, -- NULL means unlimited
    [PassingScore] DECIMAL(5,2) NOT NULL DEFAULT 70,
    [IsRandomized] BIT NOT NULL DEFAULT 0, -- Randomize question order
    [ShowCorrectAnswers] BIT NOT NULL DEFAULT 1,
    [ShowScoreImmediately] BIT NOT NULL DEFAULT 1,
    [IsPublished] BIT NOT NULL DEFAULT 0,
    [PublishedAt] DATETIME2(7) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Quizzes_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId]),
    CONSTRAINT FK_Quizzes_Lesson FOREIGN KEY ([LessonId]) REFERENCES [dbo].[Lessons]([LessonId]),
    CONSTRAINT FK_Quizzes_Subject FOREIGN KEY ([SubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_Quizzes_Difficulty FOREIGN KEY ([DifficultyId]) REFERENCES [dbo].[DifficultyLevels]([DifficultyId]),
    CONSTRAINT FK_Quizzes_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Quizzes_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Question types
CREATE TABLE [dbo].[QuestionTypes] (
    [QuestionTypeId] INT IDENTITY(1,1) PRIMARY KEY,
    [TypeName] NVARCHAR(50) NOT NULL UNIQUE,
    [Description] NVARCHAR(255) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- Questions
CREATE TABLE [dbo].[Questions] (
    [QuestionId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuestionText] NVARCHAR(MAX) NOT NULL,
    [QuestionTypeId] INT NOT NULL,
    [SubjectId] INT NOT NULL,
    [DifficultyId] INT NOT NULL,
    [Points] DECIMAL(5,2) NOT NULL DEFAULT 1,
    [Explanation] NVARCHAR(MAX) NULL,
    [ImageUrl] NVARCHAR(500) NULL,
    [VideoUrl] NVARCHAR(500) NULL,
    [Tags] NVARCHAR(500) NULL, -- Comma-separated tags
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Questions_QuestionType FOREIGN KEY ([QuestionTypeId]) REFERENCES [dbo].[QuestionTypes]([QuestionTypeId]),
    CONSTRAINT FK_Questions_Subject FOREIGN KEY ([SubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_Questions_Difficulty FOREIGN KEY ([DifficultyId]) REFERENCES [dbo].[DifficultyLevels]([DifficultyId]),
    CONSTRAINT FK_Questions_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Questions_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Answer options for multiple choice questions
CREATE TABLE [dbo].[AnswerOptions] (
    [OptionId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuestionId] INT NOT NULL,
    [OptionText] NVARCHAR(MAX) NOT NULL,
    [IsCorrect] BIT NOT NULL DEFAULT 0,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [Explanation] NVARCHAR(1000) NULL,
    [ImageUrl] NVARCHAR(500) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_AnswerOptions_Question FOREIGN KEY ([QuestionId]) REFERENCES [dbo].[Questions]([QuestionId])
);

-- Quiz-Question mapping (many-to-many)
CREATE TABLE [dbo].[QuizQuestions] (
    [QuizQuestionId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuizId] INT NOT NULL,
    [QuestionId] INT NOT NULL,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [Points] DECIMAL(5,2) NULL, -- Override question points for this quiz
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_QuizQuestions_Quiz FOREIGN KEY ([QuizId]) REFERENCES [dbo].[Quizzes]([QuizId]),
    CONSTRAINT FK_QuizQuestions_Question FOREIGN KEY ([QuestionId]) REFERENCES [dbo].[Questions]([QuestionId]),
    CONSTRAINT UQ_QuizQuestions_QuizQuestion UNIQUE ([QuizId], [QuestionId])
);

-- Quiz attempts
CREATE TABLE [dbo].[QuizAttempts] (
    [AttemptId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuizId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [AttemptNumber] INT NOT NULL,
    [StartedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CompletedAt] DATETIME2(7) NULL,
    [TimeSpentSeconds] INT NULL,
    [TotalQuestions] INT NOT NULL,
    [CorrectAnswers] INT NOT NULL DEFAULT 0,
    [TotalPoints] DECIMAL(8,2) NOT NULL DEFAULT 0,
    [MaxPoints] DECIMAL(8,2) NOT NULL,
    [ScorePercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [IsPassed] BIT NOT NULL DEFAULT 0,
    [IsCompleted] BIT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_QuizAttempts_Quiz FOREIGN KEY ([QuizId]) REFERENCES [dbo].[Quizzes]([QuizId]),
    CONSTRAINT FK_QuizAttempts_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- User answers for quiz attempts
CREATE TABLE [dbo].[QuizAnswers] (
    [AnswerId] INT IDENTITY(1,1) PRIMARY KEY,
    [AttemptId] INT NOT NULL,
    [QuestionId] INT NOT NULL,
    [SelectedOptionId] INT NULL, -- For multiple choice
    [TextAnswer] NVARCHAR(MAX) NULL, -- For text/essay questions
    [IsCorrect] BIT NOT NULL DEFAULT 0,
    [PointsEarned] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [TimeSpentSeconds] INT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_QuizAnswers_Attempt FOREIGN KEY ([AttemptId]) REFERENCES [dbo].[QuizAttempts]([AttemptId]),
    CONSTRAINT FK_QuizAnswers_Question FOREIGN KEY ([QuestionId]) REFERENCES [dbo].[Questions]([QuestionId]),
    CONSTRAINT FK_QuizAnswers_SelectedOption FOREIGN KEY ([SelectedOptionId]) REFERENCES [dbo].[AnswerOptions]([OptionId]),
    CONSTRAINT UQ_QuizAnswers_AttemptQuestion UNIQUE ([AttemptId], [QuestionId])
);

-- Gamification: Achievement badges
CREATE TABLE [dbo].[Badges] (
    [BadgeId] INT IDENTITY(1,1) PRIMARY KEY,
    [BadgeName] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500) NULL,
    [BadgeType] NVARCHAR(50) NOT NULL, -- 'Achievement', 'Progress', 'Special', 'Streak'
    [IconUrl] NVARCHAR(500) NULL,
    [ColorCode] NVARCHAR(7) NULL,
    [Points] INT NOT NULL DEFAULT 0, -- Points awarded for earning this badge
    [Criteria] NVARCHAR(MAX) NULL, -- JSON or text describing criteria
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_Badges_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Badges_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- User badges (earned badges)
CREATE TABLE [dbo].[UserBadges] (
    [UserBadgeId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [BadgeId] INT NOT NULL,
    [EarnedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [IsVisible] BIT NOT NULL DEFAULT 1, -- User can choose to hide badges
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserBadges_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_UserBadges_Badge FOREIGN KEY ([BadgeId]) REFERENCES [dbo].[Badges]([BadgeId]),
    CONSTRAINT UQ_UserBadges_UserBadge UNIQUE ([UserId], [BadgeId])
);

-- User points and scoring system
CREATE TABLE [dbo].[UserPoints] (
    [PointId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PointType] NVARCHAR(50) NOT NULL, -- 'Quiz', 'Course', 'Forum', 'Badge', 'Daily', 'Streak'
    [Points] INT NOT NULL,
    [Description] NVARCHAR(255) NULL,
    [ReferenceType] NVARCHAR(50) NULL, -- 'Quiz', 'Course', 'ForumPost', etc.
    [ReferenceId] INT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserPoints_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- User statistics and achievements
CREATE TABLE [dbo].[UserStats] (
    [StatId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL UNIQUE,
    [TotalPoints] INT NOT NULL DEFAULT 0,
    [CoursesCompleted] INT NOT NULL DEFAULT 0,
    [QuizzesTaken] INT NOT NULL DEFAULT 0,
    [QuizzesPassed] INT NOT NULL DEFAULT 0,
    [ForumPosts] INT NOT NULL DEFAULT 0,
    [ForumReplies] INT NOT NULL DEFAULT 0,
    [BadgesEarned] INT NOT NULL DEFAULT 0,
    [CurrentStreak] INT NOT NULL DEFAULT 0, -- Days
    [LongestStreak] INT NOT NULL DEFAULT 0, -- Days
    [LastActivityDate] DATE NULL,
    [JoinedDate] DATE NOT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserStats_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- Leaderboards
CREATE TABLE [dbo].[Leaderboards] (
    [LeaderboardId] INT IDENTITY(1,1) PRIMARY KEY,
    [LeaderboardName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [LeaderboardType] NVARCHAR(50) NOT NULL, -- 'Global', 'Subject', 'Course', 'Monthly', 'Weekly'
    [SubjectId] INT NULL, -- For subject-specific leaderboards
    [CourseId] INT NULL, -- For course-specific leaderboards
    [StartDate] DATE NULL, -- For time-based leaderboards
    [EndDate] DATE NULL, -- For time-based leaderboards
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_Leaderboards_Subject FOREIGN KEY ([SubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_Leaderboards_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId])
);

-- Leaderboard entries
CREATE TABLE [dbo].[LeaderboardEntries] (
    [EntryId] INT IDENTITY(1,1) PRIMARY KEY,
    [LeaderboardId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [Score] INT NOT NULL,
    [Rank] INT NOT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_LeaderboardEntries_Leaderboard FOREIGN KEY ([LeaderboardId]) REFERENCES [dbo].[Leaderboards]([LeaderboardId]),
    CONSTRAINT FK_LeaderboardEntries_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_LeaderboardEntries_LeaderboardUser UNIQUE ([LeaderboardId], [UserId])
);

-- =====================================================
-- AI-POWERED FEATURES SUPPORT
-- =====================================================

-- AI content summaries
CREATE TABLE [dbo].[ContentSummaries] (
    [SummaryId] INT IDENTITY(1,1) PRIMARY KEY,
    [ContentType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'ForumPost'
    [ContentId] INT NOT NULL,
    [SummaryText] NVARCHAR(MAX) NOT NULL,
    [SummaryType] NVARCHAR(50) NOT NULL DEFAULT 'Auto', -- 'Auto', 'Manual', 'AI-Enhanced'
    [KeyPoints] NVARCHAR(MAX) NULL, -- JSON array of key points
    [Confidence] DECIMAL(3,2) NULL, -- AI confidence score (0-1)
    [Language] NVARCHAR(10) NOT NULL DEFAULT 'en',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL, -- NULL for AI-generated
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_ContentSummaries_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_ContentSummaries_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_ContentSummaries_ContentType UNIQUE ([ContentType], [ContentId], [Language])
);

-- AI-powered recommendations
CREATE TABLE [dbo].[Recommendations] (
    [RecommendationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [RecommendationType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'Quiz', 'ForumPost'
    [RecommendedItemId] INT NOT NULL,
    [RecommendationReason] NVARCHAR(500) NULL,
    [Score] DECIMAL(5,4) NOT NULL, -- Recommendation strength (0-1)
    [Algorithm] NVARCHAR(100) NULL, -- Algorithm used for recommendation
    [IsViewed] BIT NOT NULL DEFAULT 0,
    [ViewedAt] DATETIME2(7) NULL,
    [IsClicked] BIT NOT NULL DEFAULT 0,
    [ClickedAt] DATETIME2(7) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [ExpiresAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_Recommendations_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- Learning path recommendations
CREATE TABLE [dbo].[LearningPaths] (
    [PathId] INT IDENTITY(1,1) PRIMARY KEY,
    [PathName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(1000) NULL,
    [SubjectId] INT NOT NULL,
    [DifficultyId] INT NOT NULL,
    [EstimatedDurationHours] INT NULL,
    [IsPersonalized] BIT NOT NULL DEFAULT 0, -- AI-generated personalized path
    [IsPublic] BIT NOT NULL DEFAULT 1,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    CONSTRAINT FK_LearningPaths_Subject FOREIGN KEY ([SubjectId]) REFERENCES [dbo].[Subjects]([SubjectId]),
    CONSTRAINT FK_LearningPaths_Difficulty FOREIGN KEY ([DifficultyId]) REFERENCES [dbo].[DifficultyLevels]([DifficultyId]),
    CONSTRAINT FK_LearningPaths_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_LearningPaths_UpdatedBy FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Learning path items (courses/lessons in a path)
CREATE TABLE [dbo].[LearningPathItems] (
    [PathItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [PathId] INT NOT NULL,
    [ItemType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'Quiz'
    [ItemId] INT NOT NULL,
    [SortOrder] INT NOT NULL DEFAULT 0,
    [IsRequired] BIT NOT NULL DEFAULT 1,
    [Prerequisites] NVARCHAR(MAX) NULL, -- JSON array of prerequisite item IDs
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_LearningPathItems_Path FOREIGN KEY ([PathId]) REFERENCES [dbo].[LearningPaths]([PathId])
);

-- User learning path progress
CREATE TABLE [dbo].[UserLearningPaths] (
    [UserPathId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PathId] INT NOT NULL,
    [StartedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [CompletedAt] DATETIME2(7) NULL,
    [ProgressPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [CurrentItemId] INT NULL, -- Current item in the path
    [IsCompleted] BIT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserLearningPaths_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_UserLearningPaths_Path FOREIGN KEY ([PathId]) REFERENCES [dbo].[LearningPaths]([PathId]),
    CONSTRAINT UQ_UserLearningPaths_UserPath UNIQUE ([UserId], [PathId])
);

-- AI-powered essay grading
CREATE TABLE [dbo].[EssayGrading] (
    [GradingId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuizAnswerId] INT NOT NULL, -- Links to QuizAnswers table
    [AIScore] DECIMAL(5,2) NULL, -- AI-generated score
    [AIFeedback] NVARCHAR(MAX) NULL, -- AI-generated feedback
    [HumanScore] DECIMAL(5,2) NULL, -- Human instructor score
    [HumanFeedback] NVARCHAR(MAX) NULL, -- Human instructor feedback
    [FinalScore] DECIMAL(5,2) NULL, -- Final score (AI or human)
    [GradingCriteria] NVARCHAR(MAX) NULL, -- JSON of grading criteria
    [IsAIGraded] BIT NOT NULL DEFAULT 1,
    [IsHumanReviewed] BIT NOT NULL DEFAULT 0,
    [ReviewedBy] INT NULL,
    [ReviewedAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_EssayGrading_QuizAnswer FOREIGN KEY ([QuizAnswerId]) REFERENCES [dbo].[QuizAnswers]([AnswerId]),
    CONSTRAINT FK_EssayGrading_ReviewedBy FOREIGN KEY ([ReviewedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- AI-generated quiz questions
CREATE TABLE [dbo].[AIGeneratedQuestions] (
    [AIQuestionId] INT IDENTITY(1,1) PRIMARY KEY,
    [QuestionId] INT NOT NULL,
    [SourceContent] NVARCHAR(MAX) NULL, -- Content used to generate question
    [GenerationPrompt] NVARCHAR(MAX) NULL, -- Prompt used for generation
    [AIModel] NVARCHAR(100) NULL, -- AI model used
    [Confidence] DECIMAL(3,2) NULL, -- AI confidence score
    [IsApproved] BIT NOT NULL DEFAULT 0,
    [ApprovedBy] INT NULL,
    [ApprovedAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_AIGeneratedQuestions_Question FOREIGN KEY ([QuestionId]) REFERENCES [dbo].[Questions]([QuestionId]),
    CONSTRAINT FK_AIGeneratedQuestions_ApprovedBy FOREIGN KEY ([ApprovedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- User behavior analytics for AI
CREATE TABLE [dbo].[UserBehaviorAnalytics] (
    [AnalyticsId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [SessionId] INT NULL,
    [ActionType] NVARCHAR(100) NOT NULL, -- 'PageView', 'VideoWatch', 'QuizAttempt', 'Search', etc.
    [ContentType] NVARCHAR(50) NULL, -- 'Course', 'Lesson', 'Quiz', 'ForumPost'
    [ContentId] INT NULL,
    [ActionData] NVARCHAR(MAX) NULL, -- JSON data about the action
    [TimeSpent] INT NULL, -- Time spent in seconds
    [DeviceType] NVARCHAR(50) NULL,
    [IpAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserBehaviorAnalytics_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_UserBehaviorAnalytics_Session FOREIGN KEY ([SessionId]) REFERENCES [dbo].[UserSessions]([SessionId])
);

-- =====================================================
-- NOTIFICATION AND COMMUNICATION SYSTEM
-- =====================================================

-- Notification types
CREATE TABLE [dbo].[NotificationTypes] (
    [NotificationTypeId] INT IDENTITY(1,1) PRIMARY KEY,
    [TypeName] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(255) NULL,
    [DefaultTemplate] NVARCHAR(MAX) NULL, -- Default message template
    [IsEmailEnabled] BIT NOT NULL DEFAULT 1,
    [IsPushEnabled] BIT NOT NULL DEFAULT 1,
    [IsInAppEnabled] BIT NOT NULL DEFAULT 1,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE()
);

-- User notification preferences
CREATE TABLE [dbo].[UserNotificationPreferences] (
    [PreferenceId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [NotificationTypeId] INT NOT NULL,
    [IsEmailEnabled] BIT NOT NULL DEFAULT 1,
    [IsPushEnabled] BIT NOT NULL DEFAULT 1,
    [IsInAppEnabled] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserNotificationPreferences_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_UserNotificationPreferences_Type FOREIGN KEY ([NotificationTypeId]) REFERENCES [dbo].[NotificationTypes]([NotificationTypeId]),
    CONSTRAINT UQ_UserNotificationPreferences_UserType UNIQUE ([UserId], [NotificationTypeId])
);

-- Notifications
CREATE TABLE [dbo].[Notifications] (
    [NotificationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [NotificationTypeId] INT NOT NULL,
    [Title] NVARCHAR(255) NOT NULL,
    [Message] NVARCHAR(MAX) NOT NULL,
    [ActionUrl] NVARCHAR(500) NULL, -- URL to navigate when clicked
    [ActionData] NVARCHAR(MAX) NULL, -- JSON data for the action
    [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Normal', -- 'Low', 'Normal', 'High', 'Urgent'
    [IsRead] BIT NOT NULL DEFAULT 0,
    [ReadAt] DATETIME2(7) NULL,
    [IsEmailSent] BIT NOT NULL DEFAULT 0,
    [EmailSentAt] DATETIME2(7) NULL,
    [IsPushSent] BIT NOT NULL DEFAULT 0,
    [PushSentAt] DATETIME2(7) NULL,
    [ExpiresAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_Notifications_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Notifications_Type FOREIGN KEY ([NotificationTypeId]) REFERENCES [dbo].[NotificationTypes]([NotificationTypeId])
);

-- Chat conversations
CREATE TABLE [dbo].[Conversations] (
    [ConversationId] INT IDENTITY(1,1) PRIMARY KEY,
    [ConversationType] NVARCHAR(50) NOT NULL DEFAULT 'Private', -- 'Private', 'Group', 'Course'
    [Title] NVARCHAR(255) NULL, -- For group conversations
    [Description] NVARCHAR(500) NULL,
    [CourseId] INT NULL, -- For course-related conversations
    [CreatedBy] INT NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [LastMessageAt] DATETIME2(7) NULL,
    CONSTRAINT FK_Conversations_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId]),
    CONSTRAINT FK_Conversations_CreatedBy FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Conversation participants
CREATE TABLE [dbo].[ConversationParticipants] (
    [ParticipantId] INT IDENTITY(1,1) PRIMARY KEY,
    [ConversationId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [Role] NVARCHAR(50) NOT NULL DEFAULT 'Member', -- 'Admin', 'Moderator', 'Member'
    [JoinedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [LeftAt] DATETIME2(7) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [IsMuted] BIT NOT NULL DEFAULT 0,
    [LastReadAt] DATETIME2(7) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_ConversationParticipants_Conversation FOREIGN KEY ([ConversationId]) REFERENCES [dbo].[Conversations]([ConversationId]),
    CONSTRAINT FK_ConversationParticipants_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_ConversationParticipants_ConversationUser UNIQUE ([ConversationId], [UserId])
);

-- Chat messages
CREATE TABLE [dbo].[Messages] (
    [MessageId] INT IDENTITY(1,1) PRIMARY KEY,
    [ConversationId] INT NOT NULL,
    [SenderId] INT NOT NULL,
    [MessageText] NVARCHAR(MAX) NULL,
    [MessageType] NVARCHAR(50) NOT NULL DEFAULT 'Text', -- 'Text', 'Image', 'File', 'System'
    [FileUrl] NVARCHAR(500) NULL,
    [FileName] NVARCHAR(255) NULL,
    [FileSize] BIGINT NULL,
    [MimeType] NVARCHAR(100) NULL,
    [ReplyToMessageId] INT NULL, -- For message replies
    [IsEdited] BIT NOT NULL DEFAULT 0,
    [EditedAt] DATETIME2(7) NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0,
    [DeletedAt] DATETIME2(7) NULL,
    [DeletedBy] INT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_Messages_Conversation FOREIGN KEY ([ConversationId]) REFERENCES [dbo].[Conversations]([ConversationId]),
    CONSTRAINT FK_Messages_Sender FOREIGN KEY ([SenderId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT FK_Messages_ReplyTo FOREIGN KEY ([ReplyToMessageId]) REFERENCES [dbo].[Messages]([MessageId]),
    CONSTRAINT FK_Messages_DeletedBy FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([UserId])
);

-- Message reactions (emojis, likes, etc.)
CREATE TABLE [dbo].[MessageReactions] (
    [ReactionId] INT IDENTITY(1,1) PRIMARY KEY,
    [MessageId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [ReactionType] NVARCHAR(50) NOT NULL, -- 'Like', 'Love', 'Laugh', 'Angry', etc.
    [EmojiCode] NVARCHAR(20) NULL, -- Unicode emoji code
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_MessageReactions_Message FOREIGN KEY ([MessageId]) REFERENCES [dbo].[Messages]([MessageId]),
    CONSTRAINT FK_MessageReactions_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_MessageReactions_MessageUser UNIQUE ([MessageId], [UserId])
);

-- =====================================================
-- ANALYTICS AND REPORTING TABLES
-- =====================================================

-- System analytics summary
CREATE TABLE [dbo].[SystemAnalytics] (
    [AnalyticsId] INT IDENTITY(1,1) PRIMARY KEY,
    [AnalyticsDate] DATE NOT NULL,
    [TotalUsers] INT NOT NULL DEFAULT 0,
    [ActiveUsers] INT NOT NULL DEFAULT 0, -- Users active in the last 30 days
    [NewUsers] INT NOT NULL DEFAULT 0,
    [TotalCourses] INT NOT NULL DEFAULT 0,
    [PublishedCourses] INT NOT NULL DEFAULT 0,
    [TotalEnrollments] INT NOT NULL DEFAULT 0,
    [NewEnrollments] INT NOT NULL DEFAULT 0,
    [CompletedCourses] INT NOT NULL DEFAULT 0,
    [TotalQuizAttempts] INT NOT NULL DEFAULT 0,
    [TotalForumPosts] INT NOT NULL DEFAULT 0,
    [TotalMessages] INT NOT NULL DEFAULT 0,
    [ServerUptime] DECIMAL(5,2) NULL, -- Percentage
    [AverageResponseTime] INT NULL, -- Milliseconds
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT UQ_SystemAnalytics_Date UNIQUE ([AnalyticsDate])
);

-- Course analytics
CREATE TABLE [dbo].[CourseAnalytics] (
    [CourseAnalyticsId] INT IDENTITY(1,1) PRIMARY KEY,
    [CourseId] INT NOT NULL,
    [AnalyticsDate] DATE NOT NULL,
    [ViewCount] INT NOT NULL DEFAULT 0,
    [EnrollmentCount] INT NOT NULL DEFAULT 0,
    [CompletionCount] INT NOT NULL DEFAULT 0,
    [AverageCompletionTime] INT NULL, -- Hours
    [AverageRating] DECIMAL(3,2) NULL,
    [DropoffRate] DECIMAL(5,2) NULL, -- Percentage
    [MostViewedLessonId] INT NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_CourseAnalytics_Course FOREIGN KEY ([CourseId]) REFERENCES [dbo].[Courses]([CourseId]),
    CONSTRAINT FK_CourseAnalytics_MostViewedLesson FOREIGN KEY ([MostViewedLessonId]) REFERENCES [dbo].[Lessons]([LessonId]),
    CONSTRAINT UQ_CourseAnalytics_CourseDate UNIQUE ([CourseId], [AnalyticsDate])
);

-- User learning analytics
CREATE TABLE [dbo].[UserLearningAnalytics] (
    [UserAnalyticsId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [AnalyticsDate] DATE NOT NULL,
    [TimeSpentLearning] INT NOT NULL DEFAULT 0, -- Minutes
    [LessonsCompleted] INT NOT NULL DEFAULT 0,
    [QuizzesTaken] INT NOT NULL DEFAULT 0,
    [QuizzesPassed] INT NOT NULL DEFAULT 0,
    [ForumPostsCreated] INT NOT NULL DEFAULT 0,
    [ForumRepliesCreated] INT NOT NULL DEFAULT 0,
    [PointsEarned] INT NOT NULL DEFAULT 0,
    [BadgesEarned] INT NOT NULL DEFAULT 0,
    [LoginCount] INT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_UserLearningAnalytics_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId]),
    CONSTRAINT UQ_UserLearningAnalytics_UserDate UNIQUE ([UserId], [AnalyticsDate])
);

-- Search analytics
CREATE TABLE [dbo].[SearchAnalytics] (
    [SearchId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NULL, -- NULL for anonymous searches
    [SearchQuery] NVARCHAR(500) NOT NULL,
    [SearchType] NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'Question', 'Forum', 'Global'
    [ResultCount] INT NOT NULL DEFAULT 0,
    [ClickedResultId] INT NULL, -- ID of the clicked result
    [ClickedResultType] NVARCHAR(50) NULL, -- Type of the clicked result
    [SearchDuration] INT NULL, -- Time to complete search in milliseconds
    [IpAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    [CreatedAt] DATETIME2(7) NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_SearchAnalytics_User FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users]([UserId])
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Users table indexes
CREATE NONCLUSTERED INDEX IX_Users_Email ON [dbo].[Users] ([Email]);
CREATE NONCLUSTERED INDEX IX_Users_Username ON [dbo].[Users] ([Username]);
CREATE NONCLUSTERED INDEX IX_Users_IsActive ON [dbo].[Users] ([IsActive]);
CREATE NONCLUSTERED INDEX IX_Users_TierId ON [dbo].[Users] ([TierId]);
CREATE NONCLUSTERED INDEX IX_Users_CreatedAt ON [dbo].[Users] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_Users_LastLoginAt ON [dbo].[Users] ([LastLoginAt]);

-- Courses table indexes
CREATE NONCLUSTERED INDEX IX_Courses_SubjectId ON [dbo].[Courses] ([SubjectId]);
CREATE NONCLUSTERED INDEX IX_Courses_GradeLevelId ON [dbo].[Courses] ([GradeLevelId]);
CREATE NONCLUSTERED INDEX IX_Courses_InstructorId ON [dbo].[Courses] ([InstructorId]);
CREATE NONCLUSTERED INDEX IX_Courses_IsPublished ON [dbo].[Courses] ([IsPublished]);
CREATE NONCLUSTERED INDEX IX_Courses_IsFeatured ON [dbo].[Courses] ([IsFeatured]);
CREATE NONCLUSTERED INDEX IX_Courses_CreatedAt ON [dbo].[Courses] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_Courses_AverageRating ON [dbo].[Courses] ([AverageRating]);

-- Course enrollments indexes
CREATE NONCLUSTERED INDEX IX_CourseEnrollments_UserId ON [dbo].[CourseEnrollments] ([UserId]);
CREATE NONCLUSTERED INDEX IX_CourseEnrollments_CourseId ON [dbo].[CourseEnrollments] ([CourseId]);
CREATE NONCLUSTERED INDEX IX_CourseEnrollments_EnrollmentDate ON [dbo].[CourseEnrollments] ([EnrollmentDate]);
CREATE NONCLUSTERED INDEX IX_CourseEnrollments_IsCompleted ON [dbo].[CourseEnrollments] ([IsCompleted]);

-- Lessons table indexes
CREATE NONCLUSTERED INDEX IX_Lessons_SectionId ON [dbo].[Lessons] ([SectionId]);
CREATE NONCLUSTERED INDEX IX_Lessons_IsPublished ON [dbo].[Lessons] ([IsPublished]);
CREATE NONCLUSTERED INDEX IX_Lessons_SortOrder ON [dbo].[Lessons] ([SortOrder]);

-- Forum posts indexes
CREATE NONCLUSTERED INDEX IX_ForumPosts_CategoryId ON [dbo].[ForumPosts] ([CategoryId]);
CREATE NONCLUSTERED INDEX IX_ForumPosts_UserId ON [dbo].[ForumPosts] ([UserId]);
CREATE NONCLUSTERED INDEX IX_ForumPosts_CreatedAt ON [dbo].[ForumPosts] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_ForumPosts_IsApproved ON [dbo].[ForumPosts] ([IsApproved]);
CREATE NONCLUSTERED INDEX IX_ForumPosts_IsSticky ON [dbo].[ForumPosts] ([IsSticky]);

-- Forum replies indexes
CREATE NONCLUSTERED INDEX IX_ForumReplies_PostId ON [dbo].[ForumReplies] ([PostId]);
CREATE NONCLUSTERED INDEX IX_ForumReplies_UserId ON [dbo].[ForumReplies] ([UserId]);
CREATE NONCLUSTERED INDEX IX_ForumReplies_CreatedAt ON [dbo].[ForumReplies] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_ForumReplies_ParentReplyId ON [dbo].[ForumReplies] ([ParentReplyId]);

-- Quiz attempts indexes
CREATE NONCLUSTERED INDEX IX_QuizAttempts_QuizId ON [dbo].[QuizAttempts] ([QuizId]);
CREATE NONCLUSTERED INDEX IX_QuizAttempts_UserId ON [dbo].[QuizAttempts] ([UserId]);
CREATE NONCLUSTERED INDEX IX_QuizAttempts_StartedAt ON [dbo].[QuizAttempts] ([StartedAt]);
CREATE NONCLUSTERED INDEX IX_QuizAttempts_IsCompleted ON [dbo].[QuizAttempts] ([IsCompleted]);

-- Notifications indexes
CREATE NONCLUSTERED INDEX IX_Notifications_UserId ON [dbo].[Notifications] ([UserId]);
CREATE NONCLUSTERED INDEX IX_Notifications_IsRead ON [dbo].[Notifications] ([IsRead]);
CREATE NONCLUSTERED INDEX IX_Notifications_CreatedAt ON [dbo].[Notifications] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_Notifications_Priority ON [dbo].[Notifications] ([Priority]);

-- Messages indexes
CREATE NONCLUSTERED INDEX IX_Messages_ConversationId ON [dbo].[Messages] ([ConversationId]);
CREATE NONCLUSTERED INDEX IX_Messages_SenderId ON [dbo].[Messages] ([SenderId]);
CREATE NONCLUSTERED INDEX IX_Messages_CreatedAt ON [dbo].[Messages] ([CreatedAt]);

-- User behavior analytics indexes
CREATE NONCLUSTERED INDEX IX_UserBehaviorAnalytics_UserId ON [dbo].[UserBehaviorAnalytics] ([UserId]);
CREATE NONCLUSTERED INDEX IX_UserBehaviorAnalytics_ActionType ON [dbo].[UserBehaviorAnalytics] ([ActionType]);
CREATE NONCLUSTERED INDEX IX_UserBehaviorAnalytics_CreatedAt ON [dbo].[UserBehaviorAnalytics] ([CreatedAt]);
CREATE NONCLUSTERED INDEX IX_UserBehaviorAnalytics_ContentType ON [dbo].[UserBehaviorAnalytics] ([ContentType], [ContentId]);

-- Search analytics indexes
CREATE NONCLUSTERED INDEX IX_SearchAnalytics_UserId ON [dbo].[SearchAnalytics] ([UserId]);
CREATE NONCLUSTERED INDEX IX_SearchAnalytics_SearchQuery ON [dbo].[SearchAnalytics] ([SearchQuery]);
CREATE NONCLUSTERED INDEX IX_SearchAnalytics_CreatedAt ON [dbo].[SearchAnalytics] ([CreatedAt]);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default account tiers
INSERT INTO [dbo].[AccountTiers] ([TierName], [Description], [MonthlyPrice], [MaxCourses], [MaxStorageGB], [HasAIFeatures], [HasPrioritySupport])
VALUES
    ('Free', 'Basic free account with limited features', 0.00, 5, 1, 0, 0),
    ('Premium', 'Premium account with advanced features', 19.99, NULL, 10, 1, 1),
    ('Pro', 'Professional account for instructors', 49.99, NULL, 50, 1, 1),
    ('Enterprise', 'Enterprise account for organizations', 99.99, NULL, NULL, 1, 1);

-- Insert default roles
INSERT INTO [dbo].[Roles] ([RoleName], [Description])
VALUES
    ('Guest', 'Unauthenticated user with read-only access'),
    ('Student', 'Authenticated user who can enroll in courses'),
    ('Instructor', 'User who can create and manage courses'),
    ('Moderator', 'User who can moderate forum content'),
    ('Admin', 'System administrator with full access');

-- Insert default permissions
INSERT INTO [dbo].[Permissions] ([PermissionName], [Description], [Module], [Action])
VALUES
    ('ViewCourses', 'View published courses', 'Courses', 'Read'),
    ('EnrollCourses', 'Enroll in courses', 'Courses', 'Create'),
    ('CreateCourses', 'Create new courses', 'Courses', 'Create'),
    ('ManageCourses', 'Manage own courses', 'Courses', 'Update'),
    ('DeleteCourses', 'Delete courses', 'Courses', 'Delete'),
    ('ViewForums', 'View forum posts', 'Forums', 'Read'),
    ('CreateForumPosts', 'Create forum posts', 'Forums', 'Create'),
    ('ModerateForums', 'Moderate forum content', 'Forums', 'Update'),
    ('ManageUsers', 'Manage user accounts', 'Users', 'Update'),
    ('ViewAnalytics', 'View system analytics', 'Analytics', 'Read');

-- Insert default difficulty levels
INSERT INTO [dbo].[DifficultyLevels] ([DifficultyName], [DifficultyLevel], [Description], [ColorCode])
VALUES
    ('Beginner', 1, 'Suitable for beginners with no prior knowledge', '#28a745'),
    ('Intermediate', 2, 'Requires basic understanding of the subject', '#ffc107'),
    ('Advanced', 3, 'For users with solid foundation in the subject', '#fd7e14'),
    ('Expert', 4, 'Advanced level requiring extensive knowledge', '#dc3545');

-- Insert default grade levels
INSERT INTO [dbo].[GradeLevels] ([GradeName], [GradeNumber], [Description], [AgeRangeMin], [AgeRangeMax])
VALUES
    ('Elementary', 1, 'Elementary school level', 6, 11),
    ('Middle School', 2, 'Middle school level', 12, 14),
    ('High School', 3, 'High school level', 15, 18),
    ('College', 4, 'College/University level', 18, 25),
    ('Adult', 5, 'Adult education and professional development', 25, NULL);

-- Insert default question types
INSERT INTO [dbo].[QuestionTypes] ([TypeName], [Description])
VALUES
    ('Multiple Choice', 'Single correct answer from multiple options'),
    ('Multiple Select', 'Multiple correct answers from options'),
    ('True/False', 'Binary true or false question'),
    ('Short Answer', 'Brief text response'),
    ('Essay', 'Long-form written response'),
    ('Fill in the Blank', 'Complete the missing text'),
    ('Matching', 'Match items from two lists');

-- Insert default notification types
INSERT INTO [dbo].[NotificationTypes] ([TypeName], [Description], [DefaultTemplate])
VALUES
    ('CourseEnrollment', 'User enrolled in a course', 'You have successfully enrolled in {CourseName}'),
    ('CourseCompletion', 'User completed a course', 'Congratulations! You have completed {CourseName}'),
    ('QuizResult', 'Quiz attempt result', 'Your quiz result for {QuizName}: {Score}%'),
    ('ForumReply', 'Someone replied to forum post', '{UserName} replied to your post: {PostTitle}'),
    ('NewMessage', 'New private message received', 'You have a new message from {SenderName}'),
    ('BadgeEarned', 'User earned a new badge', 'Congratulations! You earned the {BadgeName} badge'),
    ('SystemMaintenance', 'System maintenance notification', 'System maintenance scheduled for {DateTime}');

-- Insert default badges
INSERT INTO [dbo].[Badges] ([BadgeName], [Description], [BadgeType], [Points], [Criteria])
VALUES
    ('First Steps', 'Complete your first lesson', 'Achievement', 10, 'Complete 1 lesson'),
    ('Quick Learner', 'Complete 5 lessons in one day', 'Achievement', 50, 'Complete 5 lessons in 24 hours'),
    ('Course Master', 'Complete your first course', 'Achievement', 100, 'Complete 1 course'),
    ('Quiz Champion', 'Pass 10 quizzes', 'Achievement', 75, 'Pass 10 quizzes'),
    ('Forum Contributor', 'Make 10 forum posts', 'Achievement', 25, 'Create 10 forum posts'),
    ('Helpful Member', 'Receive 50 likes on forum posts', 'Achievement', 100, 'Receive 50 likes'),
    ('Streak Master', 'Maintain 30-day learning streak', 'Streak', 200, 'Learn for 30 consecutive days');

-- =====================================================
-- ADDITIONAL FEATURES AND ENHANCEMENTS
-- =====================================================

-- Create views for common queries
GO
CREATE VIEW [dbo].[vw_ActiveCourses] AS
SELECT
    c.[CourseId],
    c.[CourseTitle],
    c.[CourseCode],
    c.[Description],
    s.[SubjectName],
    gl.[GradeName],
    dl.[DifficultyName],
    u.[FirstName] + ' ' + u.[LastName] AS InstructorName,
    c.[EnrollmentCount],
    c.[AverageRating],
    c.[CreatedAt]
FROM [dbo].[Courses] c
INNER JOIN [dbo].[Subjects] s ON c.[SubjectId] = s.[SubjectId]
INNER JOIN [dbo].[GradeLevels] gl ON c.[GradeLevelId] = gl.[GradeLevelId]
INNER JOIN [dbo].[DifficultyLevels] dl ON c.[DifficultyId] = dl.[DifficultyId]
INNER JOIN [dbo].[Users] u ON c.[InstructorId] = u.[UserId]
WHERE c.[IsPublished] = 1 AND c.[IsActive] = 1;

GO
CREATE VIEW [dbo].[vw_UserProgress] AS
SELECT
    u.[UserId],
    u.[Username],
    u.[FirstName] + ' ' + u.[LastName] AS FullName,
    us.[TotalPoints],
    us.[CoursesCompleted],
    us.[QuizzesTaken],
    us.[QuizzesPassed],
    us.[BadgesEarned],
    us.[CurrentStreak],
    us.[LongestStreak]
FROM [dbo].[Users] u
INNER JOIN [dbo].[UserStats] us ON u.[UserId] = us.[UserId]
WHERE u.[IsActive] = 1;

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

GO
CREATE PROCEDURE [dbo].[sp_EnrollUserInCourse]
    @UserId INT,
    @CourseId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if user is already enrolled
    IF NOT EXISTS (SELECT 1 FROM [dbo].[CourseEnrollments] WHERE [UserId] = @UserId AND [CourseId] = @CourseId)
    BEGIN
        -- Insert enrollment
        INSERT INTO [dbo].[CourseEnrollments] ([CourseId], [UserId])
        VALUES (@CourseId, @UserId);

        -- Update course enrollment count
        UPDATE [dbo].[Courses]
        SET [EnrollmentCount] = [EnrollmentCount] + 1
        WHERE [CourseId] = @CourseId;

        SELECT 'SUCCESS' AS Result, 'User enrolled successfully' AS Message;
    END
    ELSE
    BEGIN
        SELECT 'ERROR' AS Result, 'User is already enrolled in this course' AS Message;
    END
END;

GO
-- End of VietJack Educational Platform Database Schema
-- Total Tables: 50+
-- Features: User Management, Educational Content, Community, Assessments,
--          Gamification, AI Support, Communication, Analytics
-- Designed for scalability and extensibility
