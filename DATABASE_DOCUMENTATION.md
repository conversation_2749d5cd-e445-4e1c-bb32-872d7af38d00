# VietJack Educational Platform - Database Documentation

## Overview
This document provides comprehensive documentation for the VietJack Educational Platform database schema designed for Microsoft SQL Server. The database supports a full-featured educational platform with courses, assessments, community features, gamification, and AI-powered capabilities.

## Database Statistics
- **Total Tables**: 50+
- **Total Views**: 5
- **Total Stored Procedures**: 3
- **Database Name**: VietJackEdu

## Table Categories

### 1. User Management (7 tables)
Handles user authentication, authorization, and profile management.

#### Users
Core user information and authentication data.
- **Primary Key**: user_id
- **Key Features**: 
  - Email verification support
  - Password reset functionality
  - Soft delete capability
  - Last login tracking

#### Roles
Defines system roles (Admin, Instructor, Student, Moderator).
- **Primary Key**: role_id
- **Default Roles**: Admin, Instructor, Student, Moderator

#### UserRoles
Many-to-many relationship between users and roles.
- **Primary Key**: user_role_id
- **Foreign Keys**: user_id, role_id, assigned_by

#### Permissions
Granular permissions for system operations.
- **Primary Key**: permission_id
- **Modules**: User Management, Course Management, Forum, Analytics, System

#### RolePermissions
Maps permissions to roles.
- **Primary Key**: role_permission_id
- **Foreign Keys**: role_id, permission_id

### 2. Content Structure (6 tables)
Manages the educational content hierarchy.

#### Categories
Subject categories with hierarchical support.
- **Primary Key**: category_id
- **Features**: Parent-child relationships, slugs for SEO, display ordering
- **Examples**: Mathematics, Science (Physics, Chemistry, Biology), English

#### GradeLevels
Educational grade levels.
- **Primary Key**: grade_id
- **Range**: Grade 1-12, University, Professional

#### Courses
Main course information.
- **Primary Key**: course_id
- **Key Fields**: 
  - course_code (unique identifier)
  - slug (SEO-friendly URL)
  - difficulty_level (Beginner, Intermediate, Advanced)
  - pricing information
  - enrollment limits
  - rating system
- **Features**: 
  - Soft delete support
  - Audit fields (created_by, updated_by)
  - View and enrollment tracking

#### Lessons
Individual lessons within courses.
- **Primary Key**: lesson_id
- **Types**: Video, Text, Quiz, Assignment, Mixed
- **Features**: 
  - Preview capability for non-enrolled users
  - Video duration tracking
  - Display ordering

#### CourseMaterials
Downloadable resources and materials.
- **Primary Key**: material_id
- **Features**: 
  - File metadata (size, type, MIME type)
  - Download tracking
  - Association with courses or specific lessons

### 3. Enrollment & Progress (2 tables)

#### Enrollments
Tracks user course enrollments.
- **Primary Key**: enrollment_id
- **Status Values**: Active, Completed, Dropped, Suspended
- **Features**: 
  - Progress percentage tracking
  - Certificate issuance
  - Last access tracking

#### LessonProgress
Detailed lesson completion tracking.
- **Primary Key**: progress_id
- **Features**: 
  - Time spent tracking
  - Video position saving
  - Completion percentage

### 4. Assessment System (5 tables)

#### Quizzes
Quiz definitions and settings.
- **Primary Key**: quiz_id
- **Types**: Practice, Graded, Final, Survey
- **Features**: 
  - Time limits
  - Attempt limits
  - Question shuffling
  - Passing score configuration

#### Questions
Individual quiz questions.
- **Primary Key**: question_id
- **Types**: MultipleChoice, TrueFalse, ShortAnswer, Essay, Matching
- **Difficulty Levels**: Easy, Medium, Hard
- **Features**: 
  - Point values
  - Explanations
  - Image support
  - Usage tracking

#### AnswerOptions
Multiple choice answer options.
- **Primary Key**: option_id
- **Features**: Correct answer marking, explanations

#### QuizAttempts
User quiz attempt records.
- **Primary Key**: attempt_id
- **Features**: 
  - Score tracking
  - Time taken
  - Grading workflow
  - Feedback system

#### UserAnswers
Individual question responses.
- **Primary Key**: answer_id
- **Features**: 
  - AI-generated feedback
  - Points earned
  - Correctness tracking

### 5. Community & Forum (5 tables)

#### ForumCategories
Forum category organization.
- **Primary Key**: forum_category_id
- **Examples**: General Discussion, Study Help, Course Feedback

#### ForumPosts
Main forum discussion threads.
- **Primary Key**: post_id
- **Features**: 
  - Pinning capability
  - Post locking
  - Approval workflow
  - View and reply counting
  - Last activity tracking

#### ForumReplies
Replies to forum posts.
- **Primary Key**: reply_id
- **Features**: 
  - Nested replies support
  - Solution marking
  - Like counting
  - Approval workflow

#### ForumTags
Tags for categorizing posts.
- **Primary Key**: tag_id
- **Features**: Usage counting

#### PostTags
Many-to-many relationship for post tags.

### 6. Engagement Features (4 tables)

#### Bookmarks
User bookmarks for various content types.
- **Primary Key**: bookmark_id
- **Supported Types**: Course, Lesson, Question, ForumPost
- **Features**: Personal notes

#### CourseReviews
User reviews and ratings for courses.
- **Primary Key**: review_id
- **Features**: 
  - 1-5 star rating
  - Review text
  - Approval workflow
  - Helpfulness voting

#### ReviewHelpfulness
Tracks helpful votes on reviews.
- **Primary Key**: vote_id

### 7. Gamification (4 tables)

#### Badges
Achievement badges.
- **Primary Key**: badge_id
- **Types**: Achievement, Milestone, Special
- **Rarity Levels**: Common, Rare, Epic, Legendary
- **Examples**: 
  - First Steps, Quick Learner, Quiz Master
  - Course completion milestones
  - Community contribution badges

#### UserBadges
Tracks earned badges.
- **Primary Key**: user_badge_id
- **Features**: Progress tracking (JSON)

#### UserPoints
Point transaction history.
- **Primary Key**: point_id
- **Types**: Earned, Spent, Bonus
- **Sources**: QuizCompleted, CourseCompleted, ForumPost, etc.

#### Leaderboard
Aggregated user rankings.
- **Primary Key**: leaderboard_id
- **Features**: 
  - Total points
  - Rank position
  - Various achievement counts

### 8. Notification System (2 tables)

#### Notifications
User notifications.
- **Primary Key**: notification_id
- **Types**: CourseUpdate, NewReply, BadgeEarned, QuizGraded
- **Priority Levels**: Low, Normal, High, Urgent
- **Features**: 
  - Read status tracking
  - Action URLs
  - Related content linking

#### NotificationPreferences
User notification preferences.
- **Primary Key**: preference_id
- **Channels**: Email, Push, In-App

### 9. Moderation & Reporting (1 table)

#### ContentReports
User-submitted content reports.
- **Primary Key**: report_id
- **Reported Types**: ForumPost, ForumReply, CourseReview, User
- **Reasons**: Spam, Inappropriate, Harassment, Copyright, Other
- **Status**: Pending, UnderReview, Resolved, Dismissed
- **Features**: Resolution workflow

### 10. AI-Powered Features (4 tables)

#### AIContentSummaries
AI-generated content summaries.
- **Primary Key**: summary_id
- **Features**: 
  - Key points extraction
  - Confidence scoring
  - Model version tracking

#### AILearningPaths
Personalized course recommendations.
- **Primary Key**: path_id
- **Features**: 
  - Recommendation reasoning
  - Confidence scoring
  - User acceptance tracking

#### AIQuizGeneration
AI quiz generation history.
- **Primary Key**: generation_id
- **Features**: 
  - Source content tracking
  - Generation parameters (JSON)
  - Model version tracking

#### AIEssayGrading
AI-powered essay grading.
- **Primary Key**: grading_id
- **Features**: 
  - Multi-dimensional scoring (grammar, content, structure)
  - Improvement suggestions
  - Human override capability

### 11. Messaging System (3 tables)

#### Conversations
Chat conversations.
- **Primary Key**: conversation_id
- **Types**: Private, Group

#### ConversationParticipants
Conversation membership.
- **Primary Key**: participant_id
- **Features**: Last read tracking

#### Messages
Individual messages.
- **Primary Key**: message_id
- **Features**: 
  - Attachment support
  - Edit tracking
  - Soft delete

### 12. Analytics & Tracking (3 tables)

#### UserActivityLog
Detailed user activity tracking.
- **Primary Key**: activity_id (BIGINT)
- **Activity Types**: Login, CourseView, LessonComplete, QuizAttempt
- **Features**: 
  - IP address tracking
  - User agent tracking
  - Session tracking
  - Metadata (JSON)

#### CourseAnalytics
Daily course performance metrics.
- **Primary Key**: analytics_id
- **Metrics**: 
  - View count
  - Enrollment count
  - Completion count
  - Average rating
  - Dropout rate

#### SystemAnalytics
System-wide daily metrics.
- **Primary Key**: analytics_id
- **Metrics**: 
  - User statistics
  - Course statistics
  - Revenue tracking

### 13. System Configuration (3 tables)

#### SystemSettings
Configurable system settings.
- **Primary Key**: setting_id
- **Types**: String, Number, Boolean, JSON
- **Examples**: 
  - Site configuration
  - Feature toggles
  - Default values

#### AuditLog
Admin action audit trail.
- **Primary Key**: audit_id (BIGINT)
- **Features**: 
  - Before/after values (JSON)
  - IP and user agent tracking
  - Entity tracking

#### EmailTemplates
Email template management.
- **Primary Key**: template_id
- **Features**: 
  - HTML and text versions
  - Variable substitution
  - Template activation

### 14. Media Management (2 tables)

#### MediaFiles
Centralized media file tracking.
- **Primary Key**: file_id
- **Types**: Image, Video, Document, Audio
- **Features**: 
  - File metadata
  - Dimension tracking (images/videos)
  - Duration tracking (videos/audio)
  - Download counting
  - Entity association

#### SearchHistory
User search tracking.
- **Primary Key**: search_id (BIGINT)
- **Features**: 
  - Search query logging
  - Result counting
  - Click tracking

## Database Views

### vw_ActiveCourses
Lists all published courses with instructor and category information.

### vw_UserCourseProgress
Comprehensive view of user progress across enrolled courses.

### vw_PopularCourses
Top 100 courses by enrollment and rating.

### vw_ForumActivity
Forum posts with activity metrics.

### vw_UserStatistics
Aggregated user statistics and achievements.

## Stored Procedures

### sp_EnrollUserInCourse
Enrolls a user in a course with validation and point awarding.
- **Parameters**: @user_id, @course_id
- **Features**: 
  - Duplicate enrollment check
  - Enrollment limit validation
  - Automatic point awarding

### sp_CompleteLessonProgress
Marks a lesson as completed and updates course progress.
- **Parameters**: @user_id, @lesson_id, @enrollment_id
- **Features**: 
  - Progress calculation
  - Point awarding
  - Course completion detection

### sp_UpdateLeaderboard
Recalculates leaderboard rankings.
- **Features**: 
  - Aggregates points from all sources
  - Updates rank positions
  - Efficient MERGE operation

## Indexing Strategy

### Primary Indexes
- All tables have clustered primary key indexes
- Identity columns used for auto-incrementing IDs

### Foreign Key Indexes
- Automatic indexes on all foreign key columns
- Optimizes JOIN operations

### Composite Indexes
- User-Course combinations
- User-Lesson progress tracking
- Date-based queries

### Full-Text Indexes
- Courses (title, description)
- Lessons (title, content)
- Questions (question_text, explanation)
- Forum Posts (title, content)

## Security Features

### Authentication
- Password hashing (stored as password_hash)
- Email verification tokens
- Password reset tokens with expiration

### Authorization
- Role-based access control (RBAC)
- Granular permissions system
- Permission-role mapping

### Data Protection
- Soft deletes (deleted_at columns)
- Audit logging for admin actions
- User activity tracking

### Content Moderation
- Approval workflows for forum content
- Content reporting system
- Moderation tools

## Performance Optimization

### Indexing
- Strategic indexes on frequently queried columns
- Composite indexes for common query patterns
- Full-text search for content discovery

### Denormalization
- Cached counts (enrollment_count, view_count, rating_count)
- Leaderboard table for quick rankings
- Analytics tables for reporting

### Partitioning Considerations
- UserActivityLog (by date)
- AuditLog (by date)
- Analytics tables (by date)

## Scalability Considerations

### Horizontal Scaling
- User activity can be sharded by user_id
- Course content can be cached
- Read replicas for reporting queries

### Vertical Scaling
- BIGINT for high-volume tables (activity logs)
- Efficient data types
- Proper indexing strategy

### Caching Strategy
- Course catalog
- User profiles
- Leaderboard rankings
- Popular content

## Data Integrity

### Constraints
- Foreign key constraints with CASCADE options
- CHECK constraints for enums
- UNIQUE constraints for business keys

### Triggers (Recommended)
- Update updated_at timestamps
- Maintain denormalized counts
- Enforce business rules

### Transactions
- Stored procedures use transactions
- Ensures data consistency

## Backup & Recovery

### Recommended Strategy
- Daily full backups
- Hourly differential backups
- Transaction log backups every 15 minutes
- Point-in-time recovery capability

### Critical Tables
- Users (authentication data)
- Enrollments (user progress)
- QuizAttempts (assessment records)
- Transactions (if payment system added)

## Future Enhancements

### Potential Additions
1. **Payment System**: Transactions, Subscriptions, Invoices
2. **Live Classes**: Scheduling, Attendance, Recordings
3. **Assignments**: Submission, Grading, Plagiarism Detection
4. **Certificates**: Templates, Generation, Verification
5. **Mobile App**: Push tokens, Device management
6. **Social Features**: Following, Activity feeds
7. **Advanced Analytics**: Learning analytics, Predictive models
8. **Multi-language**: Content translations, Localization

## Usage Examples

### Creating a New Course
```sql
INSERT INTO Courses (course_code, title, slug, description, category_id, instructor_id, difficulty_level)
VALUES ('CS101', 'Introduction to Programming', 'intro-programming', 'Learn programming basics', 7, 1, 'Beginner');
```

### Enrolling a User
```sql
EXEC sp_EnrollUserInCourse @user_id = 1, @course_id = 1;
```

### Querying User Progress
```sql
SELECT * FROM vw_UserCourseProgress WHERE user_id = 1;
```

### Searching Courses
```sql
SELECT * FROM Courses 
WHERE CONTAINS((title, description), 'programming OR coding')
AND is_published = 1;
```

## Maintenance Tasks

### Daily
- Update leaderboard: `EXEC sp_UpdateLeaderboard`
- Generate system analytics
- Clean up expired tokens

### Weekly
- Rebuild fragmented indexes
- Update statistics
- Archive old activity logs

### Monthly
- Review and optimize slow queries
- Analyze storage usage
- Review audit logs

## Support & Contact

For questions or issues with the database schema, please contact the development team.

---

**Version**: 1.0  
**Last Updated**: 2025-09-30  
**Database**: Microsoft SQL Server 2016+
