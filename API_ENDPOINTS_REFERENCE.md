# VietJack Educational Platform - API Endpoints Reference

This document provides a reference for API endpoints that can be built on top of the VietJack database schema. These are suggested endpoints based on the database structure and common use cases.

## 🔐 Authentication & Authorization

### POST /api/auth/register
Register a new user account.
```json
Request:
{
  "username": "student1",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "<PERSON>",
  "date_of_birth": "2000-01-15"
}

Response:
{
  "user_id": 1,
  "username": "student1",
  "email": "<EMAIL>",
  "email_verification_sent": true
}
```

### POST /api/auth/login
Authenticate user and receive token.
```json
Request:
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}

Response:
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "user_id": 1,
    "username": "student1",
    "full_name": "<PERSON>",
    "roles": ["Student"]
  }
}
```

### POST /api/auth/verify-email
Verify email address with token.

### POST /api/auth/forgot-password
Request password reset.

### POST /api/auth/reset-password
Reset password with token.

### POST /api/auth/logout
Logout user and invalidate token.

## 👤 User Management

### GET /api/users/me
Get current user profile.

### PUT /api/users/me
Update current user profile.

### PUT /api/users/me/password
Change password.

### GET /api/users/{userId}
Get user profile (public information).

### GET /api/users/{userId}/statistics
Get user statistics and achievements.
```json
Response:
{
  "user_id": 1,
  "username": "student1",
  "courses_enrolled": 5,
  "courses_completed": 2,
  "total_points": 450,
  "rank_position": 15,
  "badges_earned": 8,
  "forum_posts": 12
}
```

## 📚 Course Management

### GET /api/courses
List all published courses with filtering and pagination.
```
Query Parameters:
- category_id: Filter by category
- grade_id: Filter by grade level
- difficulty: Filter by difficulty (Beginner, Intermediate, Advanced)
- is_free: Filter free/paid courses
- search: Full-text search
- sort: Sort by (popular, rating, newest, price)
- page: Page number
- limit: Items per page
```

### GET /api/courses/{courseId}
Get detailed course information.
```json
Response:
{
  "course_id": 1,
  "course_code": "MATH101",
  "title": "Introduction to Algebra",
  "description": "Learn algebra fundamentals...",
  "category": "Mathematics",
  "grade": "Grade 9",
  "instructor": {
    "user_id": 5,
    "full_name": "Prof. Smith",
    "avatar_url": "..."
  },
  "difficulty_level": "Beginner",
  "duration_hours": 20,
  "price": 0,
  "is_free": true,
  "enrollment_count": 1250,
  "rating_average": 4.7,
  "rating_count": 89,
  "lessons_count": 15,
  "quizzes_count": 3
}
```

### POST /api/courses
Create a new course (Instructor only).

### PUT /api/courses/{courseId}
Update course information (Instructor/Admin).

### DELETE /api/courses/{courseId}
Delete course (soft delete).

### POST /api/courses/{courseId}/publish
Publish a course.

### GET /api/courses/{courseId}/lessons
Get all lessons for a course.

### GET /api/courses/{courseId}/materials
Get all materials for a course.

### GET /api/courses/{courseId}/reviews
Get course reviews.

### POST /api/courses/{courseId}/reviews
Submit a course review.

## 📖 Lesson Management

### GET /api/lessons/{lessonId}
Get lesson details.

### POST /api/lessons
Create a new lesson (Instructor only).

### PUT /api/lessons/{lessonId}
Update lesson.

### DELETE /api/lessons/{lessonId}
Delete lesson.

### POST /api/lessons/{lessonId}/complete
Mark lesson as completed.

### GET /api/lessons/{lessonId}/progress
Get user's progress for a lesson.

## 🎓 Enrollment & Progress

### POST /api/enrollments
Enroll in a course.
```json
Request:
{
  "course_id": 1
}

Response:
{
  "enrollment_id": 123,
  "course_id": 1,
  "enrollment_date": "2025-09-30T10:00:00Z",
  "status": "Active",
  "progress_percentage": 0
}
```

### GET /api/enrollments
Get user's enrollments.

### GET /api/enrollments/{enrollmentId}
Get enrollment details with progress.

### DELETE /api/enrollments/{enrollmentId}
Drop a course.

### GET /api/enrollments/{enrollmentId}/progress
Get detailed progress information.

## 📝 Quiz & Assessment

### GET /api/quizzes/{quizId}
Get quiz details.

### POST /api/quizzes
Create a quiz (Instructor only).

### PUT /api/quizzes/{quizId}
Update quiz.

### DELETE /api/quizzes/{quizId}
Delete quiz.

### POST /api/quizzes/{quizId}/start
Start a quiz attempt.
```json
Response:
{
  "attempt_id": 456,
  "quiz_id": 1,
  "started_at": "2025-09-30T10:00:00Z",
  "time_limit": 30,
  "expires_at": "2025-09-30T10:30:00Z",
  "questions": [...]
}
```

### POST /api/quizzes/attempts/{attemptId}/submit
Submit quiz answers.
```json
Request:
{
  "answers": [
    {
      "question_id": 1,
      "selected_option_id": 3
    },
    {
      "question_id": 2,
      "answer_text": "The answer is..."
    }
  ]
}

Response:
{
  "attempt_id": 456,
  "score": 85,
  "percentage": 85,
  "is_passed": true,
  "correct_answers": 17,
  "total_questions": 20
}
```

### GET /api/quizzes/attempts/{attemptId}
Get quiz attempt results.

### GET /api/quizzes/attempts/{attemptId}/review
Review quiz with correct answers.

## 💬 Forum & Community

### GET /api/forum/categories
Get all forum categories.

### GET /api/forum/posts
List forum posts with filtering.
```
Query Parameters:
- category_id: Filter by category
- tag: Filter by tag
- search: Search in title/content
- sort: Sort by (recent, popular, unanswered)
- page: Page number
- limit: Items per page
```

### GET /api/forum/posts/{postId}
Get post details with replies.

### POST /api/forum/posts
Create a new forum post.
```json
Request:
{
  "forum_category_id": 2,
  "title": "How to solve quadratic equations?",
  "content": "I need help understanding...",
  "tags": ["mathematics", "algebra"]
}
```

### PUT /api/forum/posts/{postId}
Update forum post.

### DELETE /api/forum/posts/{postId}
Delete forum post.

### POST /api/forum/posts/{postId}/replies
Reply to a forum post.

### PUT /api/forum/replies/{replyId}
Update reply.

### DELETE /api/forum/replies/{replyId}
Delete reply.

### POST /api/forum/replies/{replyId}/like
Like a reply.

### POST /api/forum/replies/{replyId}/mark-solution
Mark reply as solution (post author only).

## 🔖 Bookmarks

### GET /api/bookmarks
Get user's bookmarks.

### POST /api/bookmarks
Create a bookmark.
```json
Request:
{
  "bookmarkable_type": "Course",
  "bookmarkable_id": 1,
  "notes": "Want to review this later"
}
```

### DELETE /api/bookmarks/{bookmarkId}
Remove bookmark.

## 🏆 Gamification

### GET /api/badges
Get all available badges.

### GET /api/users/{userId}/badges
Get user's earned badges.

### GET /api/leaderboard
Get leaderboard rankings.
```
Query Parameters:
- period: Filter by period (all-time, monthly, weekly)
- limit: Number of users to return
```

### GET /api/users/{userId}/points
Get user's point history.

## 🔔 Notifications

### GET /api/notifications
Get user's notifications.
```
Query Parameters:
- is_read: Filter by read status
- type: Filter by notification type
- page: Page number
- limit: Items per page
```

### PUT /api/notifications/{notificationId}/read
Mark notification as read.

### PUT /api/notifications/read-all
Mark all notifications as read.

### GET /api/notifications/preferences
Get notification preferences.

### PUT /api/notifications/preferences
Update notification preferences.

## 💬 Messaging

### GET /api/conversations
Get user's conversations.

### GET /api/conversations/{conversationId}
Get conversation details.

### POST /api/conversations
Create a new conversation.
```json
Request:
{
  "conversation_type": "Private",
  "participant_ids": [2, 3],
  "title": "Study Group"
}
```

### GET /api/conversations/{conversationId}/messages
Get messages in a conversation.

### POST /api/conversations/{conversationId}/messages
Send a message.

### PUT /api/messages/{messageId}
Edit a message.

### DELETE /api/messages/{messageId}
Delete a message.

## 🚨 Content Moderation

### POST /api/reports
Report content.
```json
Request:
{
  "reported_type": "ForumPost",
  "reported_id": 123,
  "reason": "Spam",
  "description": "This post contains spam links"
}
```

### GET /api/reports (Admin/Moderator)
Get content reports.

### PUT /api/reports/{reportId}/resolve (Admin/Moderator)
Resolve a report.

## 🤖 AI Features

### POST /api/ai/summarize
Generate content summary.
```json
Request:
{
  "content_type": "Lesson",
  "content_id": 5
}

Response:
{
  "summary": "This lesson covers...",
  "key_points": [
    "Variables are used to store values",
    "Equations can be solved algebraically"
  ]
}
```

### GET /api/ai/recommendations
Get personalized course recommendations.

### POST /api/ai/generate-quiz
Generate quiz from content (Instructor only).

### POST /api/ai/grade-essay
Get AI grading for essay answer.

## 📊 Analytics & Reporting

### GET /api/analytics/dashboard (Admin)
Get system-wide analytics.

### GET /api/analytics/courses/{courseId} (Instructor/Admin)
Get course analytics.

### GET /api/analytics/users/{userId} (Admin)
Get user analytics.

## 🔍 Search

### GET /api/search
Global search across content.
```
Query Parameters:
- q: Search query
- type: Content type (courses, lessons, questions, forum)
- page: Page number
- limit: Items per page
```

## ⚙️ System Settings (Admin)

### GET /api/settings
Get system settings.

### PUT /api/settings/{key}
Update system setting.

### GET /api/audit-logs
Get audit logs.

## 📧 Email Templates (Admin)

### GET /api/email-templates
Get all email templates.

### PUT /api/email-templates/{templateId}
Update email template.

## 📁 Media Management

### POST /api/media/upload
Upload a file.
```
Content-Type: multipart/form-data

Response:
{
  "file_id": 789,
  "file_url": "https://cdn.example.com/files/...",
  "file_type": "Image",
  "file_size": 1024000
}
```

### DELETE /api/media/{fileId}
Delete a file.

## 🔑 API Authentication

All API endpoints (except public ones) require authentication using JWT tokens:

```
Authorization: Bearer <token>
```

## 📄 Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

## 🔒 Permission Requirements

| Endpoint | Roles Required |
|----------|---------------|
| POST /api/courses | Instructor, Admin |
| DELETE /api/courses/{id} | Instructor (owner), Admin |
| POST /api/quizzes | Instructor, Admin |
| GET /api/reports | Moderator, Admin |
| PUT /api/settings | Admin |
| GET /api/analytics | Admin |

## 📝 Notes

1. All timestamps are in ISO 8601 format (UTC)
2. Pagination uses `page` and `limit` query parameters
3. Default page size is 20 items
4. Maximum page size is 100 items
5. File uploads have a maximum size of 100MB
6. Rate limiting: 100 requests per minute per user

---

**Version**: 1.0  
**Last Updated**: 2025-09-30
