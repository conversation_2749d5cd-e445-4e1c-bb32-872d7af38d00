-- =============================================
-- VietJack Educational Platform Database Schema
-- Microsoft SQL Server
-- Version: 1.0
-- =============================================

USE master;
GO

-- Drop database if exists
IF EXISTS (SELECT name FROM sys.databases WHERE name = N'VietJackEdu')
BEGIN
    ALTER DATABASE VietJackEdu SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE VietJackEdu;
END
GO

-- Create database
CREATE DATABASE VietJackEdu;
GO

USE VietJackEdu;
GO

-- =============================================
-- 1. USER MANAGEMENT TABLES
-- =============================================

-- Users table (core authentication and profile)
CREATE TABLE Users (
    user_id INT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) NOT NULL UNIQUE,
    email NVARCHAR(100) NOT NULL UNIQUE,
    password_hash NVARCHAR(255) NOT NULL,
    full_name NVARCHAR(100) NOT NULL,
    phone NVARCHAR(20),
    avatar_url NVARCHAR(500),
    bio NVARCHAR(1000),
    date_of_birth DATE,
    gender NVARCHAR(10) CHECK (gender IN ('Male', 'Female', 'Other')),
    is_active BIT DEFAULT 1,
    is_email_verified BIT DEFAULT 0,
    email_verification_token NVARCHAR(255),
    password_reset_token NVARCHAR(255),
    password_reset_expires DATETIME,
    last_login DATETIME,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    deleted_at DATETIME NULL,
    INDEX IX_Users_Email (email),
    INDEX IX_Users_Username (username),
    INDEX IX_Users_IsActive (is_active)
);

-- Roles table
CREATE TABLE Roles (
    role_id INT IDENTITY(1,1) PRIMARY KEY,
    role_name NVARCHAR(50) NOT NULL UNIQUE,
    description NVARCHAR(255),
    created_at DATETIME DEFAULT GETDATE()
);

-- User Roles mapping (many-to-many)
CREATE TABLE UserRoles (
    user_role_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_at DATETIME DEFAULT GETDATE(),
    assigned_by INT,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES Roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES Users(user_id),
    UNIQUE (user_id, role_id)
);

-- Permissions table
CREATE TABLE Permissions (
    permission_id INT IDENTITY(1,1) PRIMARY KEY,
    permission_name NVARCHAR(100) NOT NULL UNIQUE,
    description NVARCHAR(255),
    module NVARCHAR(50),
    created_at DATETIME DEFAULT GETDATE()
);

-- Role Permissions mapping
CREATE TABLE RolePermissions (
    role_permission_id INT IDENTITY(1,1) PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    FOREIGN KEY (role_id) REFERENCES Roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES Permissions(permission_id) ON DELETE CASCADE,
    UNIQUE (role_id, permission_id)
);

-- =============================================
-- 2. CONTENT STRUCTURE TABLES
-- =============================================

-- Categories/Subjects table
CREATE TABLE Categories (
    category_id INT IDENTITY(1,1) PRIMARY KEY,
    category_name NVARCHAR(100) NOT NULL,
    slug NVARCHAR(100) NOT NULL UNIQUE,
    description NVARCHAR(500),
    parent_category_id INT NULL,
    icon_url NVARCHAR(500),
    display_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (parent_category_id) REFERENCES Categories(category_id),
    INDEX IX_Categories_Slug (slug),
    INDEX IX_Categories_ParentId (parent_category_id)
);

-- Grade Levels table
CREATE TABLE GradeLevels (
    grade_id INT IDENTITY(1,1) PRIMARY KEY,
    grade_name NVARCHAR(50) NOT NULL,
    grade_level INT NOT NULL,
    description NVARCHAR(255),
    display_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME DEFAULT GETDATE()
);

-- Courses table
CREATE TABLE Courses (
    course_id INT IDENTITY(1,1) PRIMARY KEY,
    course_code NVARCHAR(20) NOT NULL UNIQUE,
    title NVARCHAR(200) NOT NULL,
    slug NVARCHAR(200) NOT NULL UNIQUE,
    description NVARCHAR(MAX),
    short_description NVARCHAR(500),
    thumbnail_url NVARCHAR(500),
    category_id INT NOT NULL,
    grade_id INT,
    instructor_id INT NOT NULL,
    difficulty_level NVARCHAR(20) CHECK (difficulty_level IN ('Beginner', 'Intermediate', 'Advanced')),
    duration_hours DECIMAL(5,2),
    price DECIMAL(10,2) DEFAULT 0,
    is_free BIT DEFAULT 1,
    is_published BIT DEFAULT 0,
    is_featured BIT DEFAULT 0,
    enrollment_limit INT,
    prerequisites NVARCHAR(MAX),
    learning_outcomes NVARCHAR(MAX),
    view_count INT DEFAULT 0,
    enrollment_count INT DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0,
    rating_count INT DEFAULT 0,
    published_at DATETIME,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    created_by INT,
    updated_by INT,
    deleted_at DATETIME NULL,
    FOREIGN KEY (category_id) REFERENCES Categories(category_id),
    FOREIGN KEY (grade_id) REFERENCES GradeLevels(grade_id),
    FOREIGN KEY (instructor_id) REFERENCES Users(user_id),
    FOREIGN KEY (created_by) REFERENCES Users(user_id),
    FOREIGN KEY (updated_by) REFERENCES Users(user_id),
    INDEX IX_Courses_CategoryId (category_id),
    INDEX IX_Courses_InstructorId (instructor_id),
    INDEX IX_Courses_Slug (slug),
    INDEX IX_Courses_IsPublished (is_published)
);

-- Lessons table
CREATE TABLE Lessons (
    lesson_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT NOT NULL,
    title NVARCHAR(200) NOT NULL,
    slug NVARCHAR(200) NOT NULL,
    content NVARCHAR(MAX),
    lesson_type NVARCHAR(20) CHECK (lesson_type IN ('Video', 'Text', 'Quiz', 'Assignment', 'Mixed')),
    video_url NVARCHAR(500),
    video_duration INT, -- in seconds
    display_order INT DEFAULT 0,
    is_preview BIT DEFAULT 0, -- Allow preview for non-enrolled users
    is_published BIT DEFAULT 1,
    view_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    created_by INT,
    updated_by INT,
    deleted_at DATETIME NULL,
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES Users(user_id),
    FOREIGN KEY (updated_by) REFERENCES Users(user_id),
    INDEX IX_Lessons_CourseId (course_id),
    INDEX IX_Lessons_Slug (slug)
);

-- Course Materials/Resources table
CREATE TABLE CourseMaterials (
    material_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT,
    lesson_id INT,
    title NVARCHAR(200) NOT NULL,
    description NVARCHAR(500),
    file_url NVARCHAR(500) NOT NULL,
    file_type NVARCHAR(50),
    file_size BIGINT, -- in bytes
    download_count INT DEFAULT 0,
    is_downloadable BIT DEFAULT 1,
    display_order INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES Lessons(lesson_id),
    FOREIGN KEY (created_by) REFERENCES Users(user_id),
    INDEX IX_CourseMaterials_CourseId (course_id),
    INDEX IX_CourseMaterials_LessonId (lesson_id)
);

-- =============================================
-- 3. ENROLLMENT & PROGRESS TRACKING
-- =============================================

-- Course Enrollments table
CREATE TABLE Enrollments (
    enrollment_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_date DATETIME DEFAULT GETDATE(),
    completion_date DATETIME,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    status NVARCHAR(20) CHECK (status IN ('Active', 'Completed', 'Dropped', 'Suspended')) DEFAULT 'Active',
    last_accessed DATETIME,
    certificate_issued BIT DEFAULT 0,
    certificate_url NVARCHAR(500),
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    UNIQUE (user_id, course_id),
    INDEX IX_Enrollments_UserId (user_id),
    INDEX IX_Enrollments_CourseId (course_id)
);

-- Lesson Progress tracking
CREATE TABLE LessonProgress (
    progress_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    lesson_id INT NOT NULL,
    enrollment_id INT NOT NULL,
    is_completed BIT DEFAULT 0,
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    time_spent INT DEFAULT 0, -- in seconds
    last_position INT DEFAULT 0, -- for video lessons
    started_at DATETIME DEFAULT GETDATE(),
    completed_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES Lessons(lesson_id),
    FOREIGN KEY (enrollment_id) REFERENCES Enrollments(enrollment_id),
    UNIQUE (user_id, lesson_id),
    INDEX IX_LessonProgress_UserId (user_id),
    INDEX IX_LessonProgress_LessonId (lesson_id)
);

-- =============================================
-- 4. ASSESSMENT & QUIZ SYSTEM
-- =============================================

-- Quizzes table
CREATE TABLE Quizzes (
    quiz_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT,
    lesson_id INT,
    title NVARCHAR(200) NOT NULL,
    description NVARCHAR(1000),
    quiz_type NVARCHAR(20) CHECK (quiz_type IN ('Practice', 'Graded', 'Final', 'Survey')),
    time_limit INT, -- in minutes
    passing_score DECIMAL(5,2) DEFAULT 70,
    max_attempts INT DEFAULT 0, -- 0 = unlimited
    shuffle_questions BIT DEFAULT 0,
    show_correct_answers BIT DEFAULT 1,
    is_published BIT DEFAULT 1,
    display_order INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES Lessons(lesson_id),
    FOREIGN KEY (created_by) REFERENCES Users(user_id),
    INDEX IX_Quizzes_CourseId (course_id),
    INDEX IX_Quizzes_LessonId (lesson_id)
);

-- Questions table
CREATE TABLE Questions (
    question_id INT IDENTITY(1,1) PRIMARY KEY,
    quiz_id INT,
    category_id INT,
    grade_id INT,
    question_text NVARCHAR(MAX) NOT NULL,
    question_type NVARCHAR(20) CHECK (question_type IN ('MultipleChoice', 'TrueFalse', 'ShortAnswer', 'Essay', 'Matching')) NOT NULL,
    difficulty_level NVARCHAR(20) CHECK (difficulty_level IN ('Easy', 'Medium', 'Hard')),
    points DECIMAL(5,2) DEFAULT 1,
    explanation NVARCHAR(MAX),
    image_url NVARCHAR(500),
    display_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    usage_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (quiz_id) REFERENCES Quizzes(quiz_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES Categories(category_id),
    FOREIGN KEY (grade_id) REFERENCES GradeLevels(grade_id),
    FOREIGN KEY (created_by) REFERENCES Users(user_id),
    INDEX IX_Questions_QuizId (quiz_id),
    INDEX IX_Questions_CategoryId (category_id)
);

-- Answer Options table
CREATE TABLE AnswerOptions (
    option_id INT IDENTITY(1,1) PRIMARY KEY,
    question_id INT NOT NULL,
    option_text NVARCHAR(MAX) NOT NULL,
    is_correct BIT DEFAULT 0,
    display_order INT DEFAULT 0,
    explanation NVARCHAR(500),
    FOREIGN KEY (question_id) REFERENCES Questions(question_id) ON DELETE CASCADE,
    INDEX IX_AnswerOptions_QuestionId (question_id)
);

-- Quiz Attempts table
CREATE TABLE QuizAttempts (
    attempt_id INT IDENTITY(1,1) PRIMARY KEY,
    quiz_id INT NOT NULL,
    user_id INT NOT NULL,
    enrollment_id INT,
    attempt_number INT DEFAULT 1,
    score DECIMAL(5,2),
    max_score DECIMAL(5,2),
    percentage DECIMAL(5,2),
    is_passed BIT DEFAULT 0,
    time_taken INT, -- in seconds
    started_at DATETIME DEFAULT GETDATE(),
    submitted_at DATETIME,
    graded_at DATETIME,
    graded_by INT,
    feedback NVARCHAR(MAX),
    FOREIGN KEY (quiz_id) REFERENCES Quizzes(quiz_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    FOREIGN KEY (enrollment_id) REFERENCES Enrollments(enrollment_id),
    FOREIGN KEY (graded_by) REFERENCES Users(user_id),
    INDEX IX_QuizAttempts_QuizId (quiz_id),
    INDEX IX_QuizAttempts_UserId (user_id)
);

-- User Answers table
CREATE TABLE UserAnswers (
    answer_id INT IDENTITY(1,1) PRIMARY KEY,
    attempt_id INT NOT NULL,
    question_id INT NOT NULL,
    selected_option_id INT,
    answer_text NVARCHAR(MAX),
    is_correct BIT,
    points_earned DECIMAL(5,2) DEFAULT 0,
    ai_feedback NVARCHAR(MAX), -- AI-generated feedback
    answered_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (attempt_id) REFERENCES QuizAttempts(attempt_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES Questions(question_id),
    FOREIGN KEY (selected_option_id) REFERENCES AnswerOptions(option_id),
    INDEX IX_UserAnswers_AttemptId (attempt_id),
    INDEX IX_UserAnswers_QuestionId (question_id)
);

-- =============================================
-- 5. COMMUNITY & FORUM SYSTEM
-- =============================================

-- Forum Categories table
CREATE TABLE ForumCategories (
    forum_category_id INT IDENTITY(1,1) PRIMARY KEY,
    category_name NVARCHAR(100) NOT NULL,
    slug NVARCHAR(100) NOT NULL UNIQUE,
    description NVARCHAR(500),
    icon NVARCHAR(50),
    display_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME DEFAULT GETDATE()
);

-- Forum Posts table
CREATE TABLE ForumPosts (
    post_id INT IDENTITY(1,1) PRIMARY KEY,
    forum_category_id INT NOT NULL,
    user_id INT NOT NULL,
    title NVARCHAR(200) NOT NULL,
    content NVARCHAR(MAX) NOT NULL,
    slug NVARCHAR(200) NOT NULL,
    is_pinned BIT DEFAULT 0,
    is_locked BIT DEFAULT 0,
    is_approved BIT DEFAULT 1,
    view_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    last_activity DATETIME DEFAULT GETDATE(),
    last_reply_by INT,
    last_reply_at DATETIME,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    deleted_at DATETIME NULL,
    FOREIGN KEY (forum_category_id) REFERENCES ForumCategories(forum_category_id),
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    FOREIGN KEY (last_reply_by) REFERENCES Users(user_id),
    INDEX IX_ForumPosts_CategoryId (forum_category_id),
    INDEX IX_ForumPosts_UserId (user_id),
    INDEX IX_ForumPosts_Slug (slug)
);

-- Forum Replies/Comments table
CREATE TABLE ForumReplies (
    reply_id INT IDENTITY(1,1) PRIMARY KEY,
    post_id INT NOT NULL,
    user_id INT NOT NULL,
    parent_reply_id INT, -- For nested replies
    content NVARCHAR(MAX) NOT NULL,
    is_approved BIT DEFAULT 1,
    is_solution BIT DEFAULT 0, -- Mark as accepted answer
    like_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    deleted_at DATETIME NULL,
    FOREIGN KEY (post_id) REFERENCES ForumPosts(post_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    FOREIGN KEY (parent_reply_id) REFERENCES ForumReplies(reply_id),
    INDEX IX_ForumReplies_PostId (post_id),
    INDEX IX_ForumReplies_UserId (user_id)
);

-- Forum Post Tags table
CREATE TABLE ForumTags (
    tag_id INT IDENTITY(1,1) PRIMARY KEY,
    tag_name NVARCHAR(50) NOT NULL UNIQUE,
    slug NVARCHAR(50) NOT NULL UNIQUE,
    usage_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE()
);

-- Post Tags mapping
CREATE TABLE PostTags (
    post_tag_id INT IDENTITY(1,1) PRIMARY KEY,
    post_id INT NOT NULL,
    tag_id INT NOT NULL,
    FOREIGN KEY (post_id) REFERENCES ForumPosts(post_id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES ForumTags(tag_id) ON DELETE CASCADE,
    UNIQUE (post_id, tag_id)
);

-- =============================================
-- 6. BOOKMARKS & FAVORITES
-- =============================================

-- Bookmarks table (for courses, lessons, questions, posts)
CREATE TABLE Bookmarks (
    bookmark_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    bookmarkable_type NVARCHAR(50) NOT NULL, -- 'Course', 'Lesson', 'Question', 'ForumPost'
    bookmarkable_id INT NOT NULL,
    notes NVARCHAR(500),
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    INDEX IX_Bookmarks_UserId (user_id),
    INDEX IX_Bookmarks_Type (bookmarkable_type, bookmarkable_id)
);

-- =============================================
-- 7. RATINGS & REVIEWS
-- =============================================

-- Course Reviews table
CREATE TABLE CourseReviews (
    review_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT NOT NULL,
    user_id INT NOT NULL,
    enrollment_id INT,
    rating INT CHECK (rating BETWEEN 1 AND 5) NOT NULL,
    review_text NVARCHAR(2000),
    is_approved BIT DEFAULT 1,
    helpful_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    FOREIGN KEY (enrollment_id) REFERENCES Enrollments(enrollment_id),
    UNIQUE (course_id, user_id),
    INDEX IX_CourseReviews_CourseId (course_id),
    INDEX IX_CourseReviews_UserId (user_id)
);

-- Review Helpfulness votes
CREATE TABLE ReviewHelpfulness (
    vote_id INT IDENTITY(1,1) PRIMARY KEY,
    review_id INT NOT NULL,
    user_id INT NOT NULL,
    is_helpful BIT NOT NULL,
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (review_id) REFERENCES CourseReviews(review_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    UNIQUE (review_id, user_id)
);

-- =============================================
-- 8. GAMIFICATION SYSTEM
-- =============================================

-- Badges table
CREATE TABLE Badges (
    badge_id INT IDENTITY(1,1) PRIMARY KEY,
    badge_name NVARCHAR(100) NOT NULL,
    description NVARCHAR(500),
    icon_url NVARCHAR(500),
    badge_type NVARCHAR(50), -- 'Achievement', 'Milestone', 'Special'
    criteria NVARCHAR(MAX), -- JSON or description of earning criteria
    points_value INT DEFAULT 0,
    rarity NVARCHAR(20) CHECK (rarity IN ('Common', 'Rare', 'Epic', 'Legendary')),
    is_active BIT DEFAULT 1,
    created_at DATETIME DEFAULT GETDATE()
);

-- User Badges table
CREATE TABLE UserBadges (
    user_badge_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    badge_id INT NOT NULL,
    earned_at DATETIME DEFAULT GETDATE(),
    progress_data NVARCHAR(MAX), -- JSON for tracking progress
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES Badges(badge_id),
    INDEX IX_UserBadges_UserId (user_id)
);

-- User Points/Experience table
CREATE TABLE UserPoints (
    point_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    points INT NOT NULL,
    point_type NVARCHAR(50), -- 'Earned', 'Spent', 'Bonus'
    source_type NVARCHAR(50), -- 'QuizCompleted', 'CourseCompleted', 'ForumPost', etc.
    source_id INT,
    description NVARCHAR(255),
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    INDEX IX_UserPoints_UserId (user_id)
);

-- Leaderboard (materialized view or computed)
CREATE TABLE Leaderboard (
    leaderboard_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    total_points INT DEFAULT 0,
    rank_position INT,
    courses_completed INT DEFAULT 0,
    quizzes_completed INT DEFAULT 0,
    forum_posts INT DEFAULT 0,
    badges_earned INT DEFAULT 0,
    last_updated DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    UNIQUE (user_id),
    INDEX IX_Leaderboard_TotalPoints (total_points DESC)
);

-- =============================================
-- 9. NOTIFICATION SYSTEM
-- =============================================

-- Notifications table
CREATE TABLE Notifications (
    notification_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type NVARCHAR(50) NOT NULL, -- 'CourseUpdate', 'NewReply', 'BadgeEarned', 'QuizGraded', etc.
    title NVARCHAR(200) NOT NULL,
    message NVARCHAR(1000),
    related_type NVARCHAR(50), -- 'Course', 'ForumPost', 'Quiz', etc.
    related_id INT,
    action_url NVARCHAR(500),
    is_read BIT DEFAULT 0,
    is_sent BIT DEFAULT 0, -- For email/push notifications
    priority NVARCHAR(20) CHECK (priority IN ('Low', 'Normal', 'High', 'Urgent')) DEFAULT 'Normal',
    created_at DATETIME DEFAULT GETDATE(),
    read_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    INDEX IX_Notifications_UserId (user_id),
    INDEX IX_Notifications_IsRead (is_read),
    INDEX IX_Notifications_CreatedAt (created_at DESC)
);

-- Notification Preferences table
CREATE TABLE NotificationPreferences (
    preference_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type NVARCHAR(50) NOT NULL,
    email_enabled BIT DEFAULT 1,
    push_enabled BIT DEFAULT 1,
    in_app_enabled BIT DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    UNIQUE (user_id, notification_type)
);

-- =============================================
-- 10. REPORTING & MODERATION
-- =============================================

-- Content Reports table
CREATE TABLE ContentReports (
    report_id INT IDENTITY(1,1) PRIMARY KEY,
    reporter_id INT NOT NULL,
    reported_type NVARCHAR(50) NOT NULL, -- 'ForumPost', 'ForumReply', 'CourseReview', 'User'
    reported_id INT NOT NULL,
    reported_user_id INT, -- User who created the content
    reason NVARCHAR(50) NOT NULL, -- 'Spam', 'Inappropriate', 'Harassment', 'Copyright', 'Other'
    description NVARCHAR(1000),
    status NVARCHAR(20) CHECK (status IN ('Pending', 'UnderReview', 'Resolved', 'Dismissed')) DEFAULT 'Pending',
    resolution_notes NVARCHAR(1000),
    reviewed_by INT,
    reviewed_at DATETIME,
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (reporter_id) REFERENCES Users(user_id),
    FOREIGN KEY (reported_user_id) REFERENCES Users(user_id),
    FOREIGN KEY (reviewed_by) REFERENCES Users(user_id),
    INDEX IX_ContentReports_Status (status),
    INDEX IX_ContentReports_ReportedType (reported_type, reported_id)
);

-- =============================================
-- 11. AI-POWERED FEATURES
-- =============================================

-- AI Content Summaries table
CREATE TABLE AIContentSummaries (
    summary_id INT IDENTITY(1,1) PRIMARY KEY,
    content_type NVARCHAR(50) NOT NULL, -- 'Lesson', 'Course', 'ForumPost'
    content_id INT NOT NULL,
    summary_text NVARCHAR(MAX) NOT NULL,
    key_points NVARCHAR(MAX), -- JSON array
    generated_at DATETIME DEFAULT GETDATE(),
    model_version NVARCHAR(50),
    confidence_score DECIMAL(3,2),
    INDEX IX_AIContentSummaries_ContentType (content_type, content_id)
);

-- AI Learning Path Recommendations table
CREATE TABLE AILearningPaths (
    path_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    recommended_course_id INT NOT NULL,
    reason NVARCHAR(500),
    confidence_score DECIMAL(3,2),
    is_accepted BIT DEFAULT 0,
    is_dismissed BIT DEFAULT 0,
    generated_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (recommended_course_id) REFERENCES Courses(course_id),
    INDEX IX_AILearningPaths_UserId (user_id)
);

-- AI Quiz Generation History table
CREATE TABLE AIQuizGeneration (
    generation_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT,
    lesson_id INT,
    generated_quiz_id INT,
    source_content NVARCHAR(MAX),
    generation_parameters NVARCHAR(MAX), -- JSON
    questions_generated INT,
    generated_by INT,
    generated_at DATETIME DEFAULT GETDATE(),
    model_version NVARCHAR(50),
    FOREIGN KEY (course_id) REFERENCES Courses(course_id),
    FOREIGN KEY (lesson_id) REFERENCES Lessons(lesson_id),
    FOREIGN KEY (generated_quiz_id) REFERENCES Quizzes(quiz_id),
    FOREIGN KEY (generated_by) REFERENCES Users(user_id)
);

-- AI Essay Grading table
CREATE TABLE AIEssayGrading (
    grading_id INT IDENTITY(1,1) PRIMARY KEY,
    answer_id INT NOT NULL,
    attempt_id INT NOT NULL,
    question_id INT NOT NULL,
    ai_score DECIMAL(5,2),
    ai_feedback NVARCHAR(MAX),
    grammar_score DECIMAL(5,2),
    content_score DECIMAL(5,2),
    structure_score DECIMAL(5,2),
    suggestions NVARCHAR(MAX), -- JSON array
    graded_at DATETIME DEFAULT GETDATE(),
    model_version NVARCHAR(50),
    human_override BIT DEFAULT 0,
    human_score DECIMAL(5,2),
    FOREIGN KEY (answer_id) REFERENCES UserAnswers(answer_id) ON DELETE CASCADE,
    FOREIGN KEY (attempt_id) REFERENCES QuizAttempts(attempt_id),
    FOREIGN KEY (question_id) REFERENCES Questions(question_id),
    INDEX IX_AIEssayGrading_AnswerId (answer_id)
);

-- =============================================
-- 12. MESSAGING & CHAT SYSTEM
-- =============================================

-- Conversations table
CREATE TABLE Conversations (
    conversation_id INT IDENTITY(1,1) PRIMARY KEY,
    conversation_type NVARCHAR(20) CHECK (conversation_type IN ('Private', 'Group')) NOT NULL,
    title NVARCHAR(200),
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (created_by) REFERENCES Users(user_id)
);

-- Conversation Participants table
CREATE TABLE ConversationParticipants (
    participant_id INT IDENTITY(1,1) PRIMARY KEY,
    conversation_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at DATETIME DEFAULT GETDATE(),
    last_read_at DATETIME,
    is_active BIT DEFAULT 1,
    FOREIGN KEY (conversation_id) REFERENCES Conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    UNIQUE (conversation_id, user_id),
    INDEX IX_ConversationParticipants_UserId (user_id)
);

-- Messages table
CREATE TABLE Messages (
    message_id INT IDENTITY(1,1) PRIMARY KEY,
    conversation_id INT NOT NULL,
    sender_id INT NOT NULL,
    message_text NVARCHAR(MAX) NOT NULL,
    attachment_url NVARCHAR(500),
    is_edited BIT DEFAULT 0,
    is_deleted BIT DEFAULT 0,
    sent_at DATETIME DEFAULT GETDATE(),
    edited_at DATETIME,
    FOREIGN KEY (conversation_id) REFERENCES Conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES Users(user_id),
    INDEX IX_Messages_ConversationId (conversation_id),
    INDEX IX_Messages_SentAt (sent_at DESC)
);

-- =============================================
-- 13. ANALYTICS & TRACKING
-- =============================================

-- User Activity Log table
CREATE TABLE UserActivityLog (
    activity_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_id INT,
    activity_type NVARCHAR(50) NOT NULL, -- 'Login', 'CourseView', 'LessonComplete', 'QuizAttempt', etc.
    entity_type NVARCHAR(50),
    entity_id INT,
    ip_address NVARCHAR(45),
    user_agent NVARCHAR(500),
    session_id NVARCHAR(100),
    metadata NVARCHAR(MAX), -- JSON for additional data
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    INDEX IX_UserActivityLog_UserId (user_id),
    INDEX IX_UserActivityLog_ActivityType (activity_type),
    INDEX IX_UserActivityLog_CreatedAt (created_at DESC)
);

-- Course Analytics table
CREATE TABLE CourseAnalytics (
    analytics_id INT IDENTITY(1,1) PRIMARY KEY,
    course_id INT NOT NULL,
    date DATE NOT NULL,
    view_count INT DEFAULT 0,
    enrollment_count INT DEFAULT 0,
    completion_count INT DEFAULT 0,
    average_rating DECIMAL(3,2),
    average_completion_time INT, -- in hours
    dropout_rate DECIMAL(5,2),
    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
    UNIQUE (course_id, date),
    INDEX IX_CourseAnalytics_Date (date DESC)
);

-- System Analytics Dashboard table
CREATE TABLE SystemAnalytics (
    analytics_id INT IDENTITY(1,1) PRIMARY KEY,
    date DATE NOT NULL UNIQUE,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    total_courses INT DEFAULT 0,
    total_enrollments INT DEFAULT 0,
    total_quiz_attempts INT DEFAULT 0,
    total_forum_posts INT DEFAULT 0,
    revenue DECIMAL(12,2) DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE()
);

-- =============================================
-- 14. SYSTEM CONFIGURATION & SETTINGS
-- =============================================

-- System Settings table
CREATE TABLE SystemSettings (
    setting_id INT IDENTITY(1,1) PRIMARY KEY,
    setting_key NVARCHAR(100) NOT NULL UNIQUE,
    setting_value NVARCHAR(MAX),
    setting_type NVARCHAR(20) CHECK (setting_type IN ('String', 'Number', 'Boolean', 'JSON')),
    description NVARCHAR(500),
    is_public BIT DEFAULT 0, -- Can be accessed by non-admin users
    updated_at DATETIME DEFAULT GETDATE(),
    updated_by INT,
    FOREIGN KEY (updated_by) REFERENCES Users(user_id)
);

-- Audit Log table (for tracking admin actions)
CREATE TABLE AuditLog (
    audit_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_id INT,
    action NVARCHAR(100) NOT NULL, -- 'Create', 'Update', 'Delete', 'Approve', 'Suspend', etc.
    entity_type NVARCHAR(50) NOT NULL, -- 'User', 'Course', 'ForumPost', etc.
    entity_id INT,
    old_values NVARCHAR(MAX), -- JSON
    new_values NVARCHAR(MAX), -- JSON
    ip_address NVARCHAR(45),
    user_agent NVARCHAR(500),
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    INDEX IX_AuditLog_UserId (user_id),
    INDEX IX_AuditLog_EntityType (entity_type, entity_id),
    INDEX IX_AuditLog_CreatedAt (created_at DESC)
);

-- Email Templates table
CREATE TABLE EmailTemplates (
    template_id INT IDENTITY(1,1) PRIMARY KEY,
    template_name NVARCHAR(100) NOT NULL UNIQUE,
    subject NVARCHAR(200) NOT NULL,
    body_html NVARCHAR(MAX) NOT NULL,
    body_text NVARCHAR(MAX),
    variables NVARCHAR(MAX), -- JSON array of available variables
    is_active BIT DEFAULT 1,
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE()
);

-- =============================================
-- 15. FILE STORAGE & MEDIA
-- =============================================

-- Media Files table
CREATE TABLE MediaFiles (
    file_id INT IDENTITY(1,1) PRIMARY KEY,
    file_name NVARCHAR(255) NOT NULL,
    file_path NVARCHAR(500) NOT NULL,
    file_url NVARCHAR(500) NOT NULL,
    file_type NVARCHAR(50), -- 'Image', 'Video', 'Document', 'Audio'
    mime_type NVARCHAR(100),
    file_size BIGINT, -- in bytes
    width INT, -- for images/videos
    height INT, -- for images/videos
    duration INT, -- for videos/audio in seconds
    uploaded_by INT NOT NULL,
    entity_type NVARCHAR(50), -- 'Course', 'Lesson', 'User', 'ForumPost'
    entity_id INT,
    is_public BIT DEFAULT 1,
    download_count INT DEFAULT 0,
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (uploaded_by) REFERENCES Users(user_id),
    INDEX IX_MediaFiles_UploadedBy (uploaded_by),
    INDEX IX_MediaFiles_EntityType (entity_type, entity_id)
);

-- =============================================
-- 16. SEARCH & INDEXING (for full-text search)
-- =============================================

-- Search History table
CREATE TABLE SearchHistory (
    search_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_id INT,
    search_query NVARCHAR(500) NOT NULL,
    search_type NVARCHAR(50), -- 'Course', 'Question', 'ForumPost', 'All'
    results_count INT,
    clicked_result_id INT,
    clicked_result_type NVARCHAR(50),
    created_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES Users(user_id),
    INDEX IX_SearchHistory_UserId (user_id),
    INDEX IX_SearchHistory_Query (search_query)
);

-- =============================================
-- INSERT INITIAL DATA
-- =============================================

-- Insert default roles
INSERT INTO Roles (role_name, description) VALUES
('Admin', 'System administrator with full access'),
('Instructor', 'Can create and manage courses'),
('Student', 'Regular user who can enroll in courses'),
('Moderator', 'Can moderate forum content');

-- Insert default permissions
INSERT INTO Permissions (permission_name, description, module) VALUES
('user.view', 'View users', 'User Management'),
('user.create', 'Create users', 'User Management'),
('user.edit', 'Edit users', 'User Management'),
('user.delete', 'Delete users', 'User Management'),
('user.suspend', 'Suspend/activate users', 'User Management'),
('course.view', 'View courses', 'Course Management'),
('course.create', 'Create courses', 'Course Management'),
('course.edit', 'Edit courses', 'Course Management'),
('course.delete', 'Delete courses', 'Course Management'),
('course.publish', 'Publish courses', 'Course Management'),
('lesson.create', 'Create lessons', 'Lesson Management'),
('lesson.edit', 'Edit lessons', 'Lesson Management'),
('lesson.delete', 'Delete lessons', 'Lesson Management'),
('quiz.create', 'Create quizzes', 'Quiz Management'),
('quiz.edit', 'Edit quizzes', 'Quiz Management'),
('quiz.delete', 'Delete quizzes', 'Quiz Management'),
('quiz.grade', 'Grade quizzes', 'Quiz Management'),
('forum.post', 'Create forum posts', 'Forum'),
('forum.reply', 'Reply to forum posts', 'Forum'),
('forum.moderate', 'Moderate forum content', 'Forum'),
('report.view', 'View reports', 'Moderation'),
('report.resolve', 'Resolve reports', 'Moderation'),
('analytics.view', 'View analytics', 'Analytics'),
('system.settings', 'Manage system settings', 'System');

-- Assign permissions to Admin role
INSERT INTO RolePermissions (role_id, permission_id)
SELECT 1, permission_id FROM Permissions;

-- Assign permissions to Instructor role
INSERT INTO RolePermissions (role_id, permission_id)
SELECT 2, permission_id FROM Permissions
WHERE permission_name IN (
    'course.view', 'course.create', 'course.edit', 'course.publish',
    'lesson.create', 'lesson.edit', 'lesson.delete',
    'quiz.create', 'quiz.edit', 'quiz.delete', 'quiz.grade',
    'forum.post', 'forum.reply'
);

-- Assign permissions to Student role
INSERT INTO RolePermissions (role_id, permission_id)
SELECT 3, permission_id FROM Permissions
WHERE permission_name IN ('course.view', 'forum.post', 'forum.reply');

-- Assign permissions to Moderator role
INSERT INTO RolePermissions (role_id, permission_id)
SELECT 4, permission_id FROM Permissions
WHERE permission_name IN (
    'course.view', 'forum.post', 'forum.reply', 'forum.moderate',
    'report.view', 'report.resolve'
);

-- Insert default grade levels
INSERT INTO GradeLevels (grade_name, grade_level, display_order) VALUES
('Grade 1', 1, 1),
('Grade 2', 2, 2),
('Grade 3', 3, 3),
('Grade 4', 4, 4),
('Grade 5', 5, 5),
('Grade 6', 6, 6),
('Grade 7', 7, 7),
('Grade 8', 8, 8),
('Grade 9', 9, 9),
('Grade 10', 10, 10),
('Grade 11', 11, 11),
('Grade 12', 12, 12),
('University', 13, 13),
('Professional', 14, 14);

-- Insert default categories
INSERT INTO Categories (category_name, slug, description, display_order) VALUES
('Mathematics', 'mathematics', 'Math courses and lessons', 1),
('Science', 'science', 'Science courses including Physics, Chemistry, Biology', 2),
('English', 'english', 'English language and literature', 3),
('Vietnamese', 'vietnamese', 'Vietnamese language and literature', 4),
('History', 'history', 'History and social studies', 5),
('Geography', 'geography', 'Geography courses', 6),
('Computer Science', 'computer-science', 'Programming and computer science', 7),
('Foreign Languages', 'foreign-languages', 'Foreign language courses', 8);

-- Insert subcategories for Science
INSERT INTO Categories (category_name, slug, description, parent_category_id, display_order) VALUES
('Physics', 'physics', 'Physics courses', 2, 1),
('Chemistry', 'chemistry', 'Chemistry courses', 2, 2),
('Biology', 'biology', 'Biology courses', 2, 3);

-- Insert default forum categories
INSERT INTO ForumCategories (category_name, slug, description, display_order) VALUES
('General Discussion', 'general-discussion', 'General topics and discussions', 1),
('Study Help', 'study-help', 'Ask questions and get help with your studies', 2),
('Course Feedback', 'course-feedback', 'Share feedback about courses', 3),
('Announcements', 'announcements', 'Official announcements', 4),
('Off Topic', 'off-topic', 'Non-academic discussions', 5);

-- Insert default badges
INSERT INTO Badges (badge_name, description, badge_type, points_value, rarity) VALUES
('First Steps', 'Complete your first lesson', 'Achievement', 10, 'Common'),
('Quick Learner', 'Complete a course in less than a week', 'Achievement', 50, 'Rare'),
('Quiz Master', 'Score 100% on 10 quizzes', 'Achievement', 100, 'Epic'),
('Helpful Member', 'Receive 50 helpful votes on forum replies', 'Achievement', 75, 'Rare'),
('Course Completed', 'Complete your first course', 'Milestone', 50, 'Common'),
('5 Courses Completed', 'Complete 5 courses', 'Milestone', 200, 'Rare'),
('10 Courses Completed', 'Complete 10 courses', 'Milestone', 500, 'Epic'),
('Perfect Score', 'Get 100% on a final exam', 'Achievement', 100, 'Rare'),
('Early Bird', 'Log in for 7 consecutive days', 'Achievement', 30, 'Common'),
('Dedicated Student', 'Log in for 30 consecutive days', 'Achievement', 150, 'Epic'),
('Knowledge Sharer', 'Post 100 helpful forum replies', 'Achievement', 200, 'Epic'),
('Top Contributor', 'Reach top 10 on leaderboard', 'Achievement', 500, 'Legendary');

-- Insert default system settings
INSERT INTO SystemSettings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site.name', 'VietJack Educational Platform', 'String', 'Website name', 1),
('site.description', 'Learn anything, anytime, anywhere', 'String', 'Website description', 1),
('site.email', '<EMAIL>', 'String', 'Contact email', 1),
('site.maintenance_mode', 'false', 'Boolean', 'Enable maintenance mode', 0),
('course.max_enrollment', '1000', 'Number', 'Maximum enrollments per course', 0),
('quiz.default_time_limit', '60', 'Number', 'Default quiz time limit in minutes', 0),
('forum.posts_per_page', '20', 'Number', 'Forum posts per page', 1),
('gamification.enabled', 'true', 'Boolean', 'Enable gamification features', 1),
('ai.content_summary_enabled', 'true', 'Boolean', 'Enable AI content summaries', 0),
('ai.quiz_generation_enabled', 'true', 'Boolean', 'Enable AI quiz generation', 0),
('ai.essay_grading_enabled', 'true', 'Boolean', 'Enable AI essay grading', 0),
('notification.email_enabled', 'true', 'Boolean', 'Enable email notifications', 0),
('notification.push_enabled', 'true', 'Boolean', 'Enable push notifications', 0);

-- Insert default email templates
INSERT INTO EmailTemplates (template_name, subject, body_html, body_text) VALUES
('welcome_email', 'Welcome to VietJack!',
 '<h1>Welcome {{user_name}}!</h1><p>Thank you for joining VietJack Educational Platform.</p>',
 'Welcome {{user_name}}! Thank you for joining VietJack Educational Platform.'),
('course_enrollment', 'You are enrolled in {{course_name}}',
 '<h1>Congratulations!</h1><p>You have successfully enrolled in {{course_name}}.</p>',
 'Congratulations! You have successfully enrolled in {{course_name}}.'),
('quiz_graded', 'Your quiz has been graded',
 '<h1>Quiz Results</h1><p>Your quiz "{{quiz_name}}" has been graded. Score: {{score}}%</p>',
 'Your quiz "{{quiz_name}}" has been graded. Score: {{score}}%'),
('password_reset', 'Reset your password',
 '<h1>Password Reset</h1><p>Click here to reset your password: {{reset_link}}</p>',
 'Click here to reset your password: {{reset_link}}');

-- =============================================
-- USEFUL VIEWS
-- =============================================

-- View: Active courses with instructor information
GO
CREATE VIEW vw_ActiveCourses AS
SELECT
    c.course_id,
    c.course_code,
    c.title,
    c.slug,
    c.short_description,
    c.thumbnail_url,
    cat.category_name,
    g.grade_name,
    u.full_name AS instructor_name,
    u.email AS instructor_email,
    c.difficulty_level,
    c.duration_hours,
    c.price,
    c.is_free,
    c.enrollment_count,
    c.rating_average,
    c.rating_count,
    c.published_at,
    c.created_at
FROM Courses c
INNER JOIN Users u ON c.instructor_id = u.user_id
INNER JOIN Categories cat ON c.category_id = cat.category_id
LEFT JOIN GradeLevels g ON c.grade_id = g.grade_id
WHERE c.is_published = 1
  AND c.deleted_at IS NULL
  AND u.is_active = 1;
GO

-- View: User course progress
CREATE VIEW vw_UserCourseProgress AS
SELECT
    e.enrollment_id,
    e.user_id,
    u.full_name AS user_name,
    e.course_id,
    c.title AS course_title,
    e.enrollment_date,
    e.progress_percentage,
    e.status,
    e.last_accessed,
    COUNT(DISTINCT lp.lesson_id) AS lessons_completed,
    COUNT(DISTINCT l.lesson_id) AS total_lessons,
    AVG(qa.percentage) AS average_quiz_score
FROM Enrollments e
INNER JOIN Users u ON e.user_id = u.user_id
INNER JOIN Courses c ON e.course_id = c.course_id
LEFT JOIN Lessons l ON c.course_id = l.course_id AND l.is_published = 1
LEFT JOIN LessonProgress lp ON e.user_id = lp.user_id AND l.lesson_id = lp.lesson_id AND lp.is_completed = 1
LEFT JOIN QuizAttempts qa ON e.user_id = qa.user_id AND e.enrollment_id = qa.enrollment_id
WHERE e.status = 'Active'
GROUP BY
    e.enrollment_id, e.user_id, u.full_name, e.course_id, c.title,
    e.enrollment_date, e.progress_percentage, e.status, e.last_accessed;
GO

-- View: Popular courses
CREATE VIEW vw_PopularCourses AS
SELECT TOP 100
    c.course_id,
    c.title,
    c.slug,
    c.thumbnail_url,
    cat.category_name,
    u.full_name AS instructor_name,
    c.enrollment_count,
    c.rating_average,
    c.rating_count,
    c.view_count,
    c.price,
    c.is_free
FROM Courses c
INNER JOIN Users u ON c.instructor_id = u.user_id
INNER JOIN Categories cat ON c.category_id = cat.category_id
WHERE c.is_published = 1 AND c.deleted_at IS NULL
ORDER BY c.enrollment_count DESC, c.rating_average DESC;
GO

-- View: Forum activity summary
CREATE VIEW vw_ForumActivity AS
SELECT
    fp.post_id,
    fp.title,
    fp.slug,
    fc.category_name,
    u.full_name AS author_name,
    u.username AS author_username,
    fp.view_count,
    fp.reply_count,
    fp.is_pinned,
    fp.created_at,
    fp.last_activity,
    u2.full_name AS last_reply_by_name
FROM ForumPosts fp
INNER JOIN ForumCategories fc ON fp.forum_category_id = fc.forum_category_id
INNER JOIN Users u ON fp.user_id = u.user_id
LEFT JOIN Users u2 ON fp.last_reply_by = u2.user_id
WHERE fp.deleted_at IS NULL AND fp.is_approved = 1;
GO

-- View: User statistics
CREATE VIEW vw_UserStatistics AS
SELECT
    u.user_id,
    u.username,
    u.full_name,
    u.email,
    u.created_at,
    u.last_login,
    COUNT(DISTINCT e.enrollment_id) AS courses_enrolled,
    COUNT(DISTINCT CASE WHEN e.status = 'Completed' THEN e.enrollment_id END) AS courses_completed,
    COUNT(DISTINCT qa.attempt_id) AS quizzes_taken,
    COUNT(DISTINCT fp.post_id) AS forum_posts,
    COUNT(DISTINCT fr.reply_id) AS forum_replies,
    COALESCE(l.total_points, 0) AS total_points,
    COALESCE(l.rank_position, 0) AS leaderboard_rank,
    COUNT(DISTINCT ub.badge_id) AS badges_earned
FROM Users u
LEFT JOIN Enrollments e ON u.user_id = e.user_id
LEFT JOIN QuizAttempts qa ON u.user_id = qa.user_id
LEFT JOIN ForumPosts fp ON u.user_id = fp.user_id AND fp.deleted_at IS NULL
LEFT JOIN ForumReplies fr ON u.user_id = fr.user_id AND fr.deleted_at IS NULL
LEFT JOIN Leaderboard l ON u.user_id = l.user_id
LEFT JOIN UserBadges ub ON u.user_id = ub.user_id
WHERE u.is_active = 1 AND u.deleted_at IS NULL
GROUP BY
    u.user_id, u.username, u.full_name, u.email, u.created_at, u.last_login,
    l.total_points, l.rank_position;
GO

-- =============================================
-- USEFUL STORED PROCEDURES
-- =============================================

-- Procedure: Enroll user in course
CREATE PROCEDURE sp_EnrollUserInCourse
    @user_id INT,
    @course_id INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if already enrolled
    IF EXISTS (SELECT 1 FROM Enrollments WHERE user_id = @user_id AND course_id = @course_id)
    BEGIN
        RAISERROR('User is already enrolled in this course', 16, 1);
        RETURN;
    END

    -- Check enrollment limit
    DECLARE @enrollment_limit INT, @current_enrollments INT;
    SELECT @enrollment_limit = enrollment_limit, @current_enrollments = enrollment_count
    FROM Courses WHERE course_id = @course_id;

    IF @enrollment_limit IS NOT NULL AND @current_enrollments >= @enrollment_limit
    BEGIN
        RAISERROR('Course enrollment limit reached', 16, 1);
        RETURN;
    END

    BEGIN TRANSACTION;

    -- Create enrollment
    INSERT INTO Enrollments (user_id, course_id, enrollment_date, status)
    VALUES (@user_id, @course_id, GETDATE(), 'Active');

    -- Update course enrollment count
    UPDATE Courses
    SET enrollment_count = enrollment_count + 1
    WHERE course_id = @course_id;

    -- Award points
    INSERT INTO UserPoints (user_id, points, point_type, source_type, source_id, description)
    VALUES (@user_id, 10, 'Earned', 'CourseEnrollment', @course_id, 'Enrolled in a course');

    COMMIT TRANSACTION;
END;
GO

-- Procedure: Complete lesson
CREATE PROCEDURE sp_CompleteLessonProgress
    @user_id INT,
    @lesson_id INT,
    @enrollment_id INT
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    -- Update or insert lesson progress
    IF EXISTS (SELECT 1 FROM LessonProgress WHERE user_id = @user_id AND lesson_id = @lesson_id)
    BEGIN
        UPDATE LessonProgress
        SET is_completed = 1,
            completion_percentage = 100,
            completed_at = GETDATE()
        WHERE user_id = @user_id AND lesson_id = @lesson_id;
    END
    ELSE
    BEGIN
        INSERT INTO LessonProgress (user_id, lesson_id, enrollment_id, is_completed, completion_percentage, completed_at)
        VALUES (@user_id, @lesson_id, @enrollment_id, 1, 100, GETDATE());
    END

    -- Award points
    INSERT INTO UserPoints (user_id, points, point_type, source_type, source_id, description)
    VALUES (@user_id, 5, 'Earned', 'LessonCompleted', @lesson_id, 'Completed a lesson');

    -- Update enrollment progress
    DECLARE @course_id INT, @total_lessons INT, @completed_lessons INT;

    SELECT @course_id = course_id FROM Lessons WHERE lesson_id = @lesson_id;
    SELECT @total_lessons = COUNT(*) FROM Lessons WHERE course_id = @course_id AND is_published = 1;
    SELECT @completed_lessons = COUNT(*)
    FROM LessonProgress lp
    INNER JOIN Lessons l ON lp.lesson_id = l.lesson_id
    WHERE lp.user_id = @user_id AND l.course_id = @course_id AND lp.is_completed = 1;

    DECLARE @progress_percentage DECIMAL(5,2) = (@completed_lessons * 100.0) / @total_lessons;

    UPDATE Enrollments
    SET progress_percentage = @progress_percentage,
        last_accessed = GETDATE()
    WHERE enrollment_id = @enrollment_id;

    -- Check if course is completed
    IF @progress_percentage >= 100
    BEGIN
        UPDATE Enrollments
        SET status = 'Completed',
            completion_date = GETDATE()
        WHERE enrollment_id = @enrollment_id;

        -- Award course completion points
        INSERT INTO UserPoints (user_id, points, point_type, source_type, source_id, description)
        VALUES (@user_id, 100, 'Earned', 'CourseCompleted', @course_id, 'Completed a course');
    END

    COMMIT TRANSACTION;
END;
GO

-- Procedure: Update leaderboard
CREATE PROCEDURE sp_UpdateLeaderboard
AS
BEGIN
    SET NOCOUNT ON;

    -- Update or insert leaderboard entries
    MERGE Leaderboard AS target
    USING (
        SELECT
            u.user_id,
            COALESCE(SUM(up.points), 0) AS total_points,
            COUNT(DISTINCT CASE WHEN e.status = 'Completed' THEN e.enrollment_id END) AS courses_completed,
            COUNT(DISTINCT qa.attempt_id) AS quizzes_completed,
            COUNT(DISTINCT fp.post_id) + COUNT(DISTINCT fr.reply_id) AS forum_posts,
            COUNT(DISTINCT ub.badge_id) AS badges_earned
        FROM Users u
        LEFT JOIN UserPoints up ON u.user_id = up.user_id
        LEFT JOIN Enrollments e ON u.user_id = e.user_id
        LEFT JOIN QuizAttempts qa ON u.user_id = qa.user_id
        LEFT JOIN ForumPosts fp ON u.user_id = fp.user_id AND fp.deleted_at IS NULL
        LEFT JOIN ForumReplies fr ON u.user_id = fr.user_id AND fr.deleted_at IS NULL
        LEFT JOIN UserBadges ub ON u.user_id = ub.user_id
        WHERE u.is_active = 1 AND u.deleted_at IS NULL
        GROUP BY u.user_id
    ) AS source
    ON target.user_id = source.user_id
    WHEN MATCHED THEN
        UPDATE SET
            total_points = source.total_points,
            courses_completed = source.courses_completed,
            quizzes_completed = source.quizzes_completed,
            forum_posts = source.forum_posts,
            badges_earned = source.badges_earned,
            last_updated = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (user_id, total_points, courses_completed, quizzes_completed, forum_posts, badges_earned)
        VALUES (source.user_id, source.total_points, source.courses_completed,
                source.quizzes_completed, source.forum_posts, source.badges_earned);

    -- Update rank positions
    WITH RankedUsers AS (
        SELECT user_id, ROW_NUMBER() OVER (ORDER BY total_points DESC) AS rank_pos
        FROM Leaderboard
    )
    UPDATE l
    SET rank_position = ru.rank_pos
    FROM Leaderboard l
    INNER JOIN RankedUsers ru ON l.user_id = ru.user_id;
END;
GO

-- =============================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =============================================

-- Additional composite indexes for common queries
CREATE INDEX IX_Enrollments_UserCourse ON Enrollments(user_id, course_id, status);
CREATE INDEX IX_LessonProgress_UserLesson ON LessonProgress(user_id, lesson_id, is_completed);
CREATE INDEX IX_QuizAttempts_UserQuiz ON QuizAttempts(user_id, quiz_id, submitted_at DESC);
CREATE INDEX IX_ForumPosts_CategoryDate ON ForumPosts(forum_category_id, created_at DESC);
CREATE INDEX IX_Notifications_UserRead ON Notifications(user_id, is_read, created_at DESC);
CREATE INDEX IX_UserActivityLog_UserDate ON UserActivityLog(user_id, created_at DESC);

-- Full-text indexes for search functionality
CREATE FULLTEXT CATALOG ftCatalog AS DEFAULT;
GO

CREATE FULLTEXT INDEX ON Courses(title, description, short_description)
KEY INDEX PK__Courses__8F1EF7AE ON ftCatalog;
GO

CREATE FULLTEXT INDEX ON Lessons(title, content)
KEY INDEX PK__Lessons__6421F7BE ON ftCatalog;
GO

CREATE FULLTEXT INDEX ON Questions(question_text, explanation)
KEY INDEX PK__Question__2D2E8C9C ON ftCatalog;
GO

CREATE FULLTEXT INDEX ON ForumPosts(title, content)
KEY INDEX PK__ForumPos__3ED78766 ON ftCatalog;
GO

PRINT 'Database schema created successfully!';
PRINT 'Total tables created: 50+';
PRINT 'Total views created: 5';
PRINT 'Total stored procedures created: 3';
PRINT '';
PRINT 'Next steps:';
PRINT '1. Create an admin user account';
PRINT '2. Configure system settings';
PRINT '3. Set up email templates';
PRINT '4. Create initial course content';
PRINT '5. Test the application functionality';
GO

